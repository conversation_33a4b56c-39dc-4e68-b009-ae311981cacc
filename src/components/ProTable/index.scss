@import "@/styles/mixins/responsive";
.table-box {
  @include responsive-container;
  :deep(.el-table) {
    @include responsive-table;
    .el-table__header-wrapper {
      .el-table__header {
        th.el-table__cell {
          .cell {
            display: inline-block !important;
            width: 100% !important;
            max-width: 100% !important;
            overflow: hidden !important;
            text-overflow: ellipsis !important;
            white-space: nowrap !important;

            @include respond-to(md) {
              font-size: var(--font-size-xs) !important;
            }
          }
        }
      }
    }

    /* ProTable特有的响应式优化 */
    @include respond-to(md) {
      .el-table__body {
        .el-table__row {
          .el-table__cell {
            .cell {
              padding: 4px 2px !important;
              font-size: var(--font-size-xs) !important;
            }
          }
        }
      }

      /* 隐藏操作列中的部分按钮 */
      .table-operation {
        .el-button:not(.el-button--primary) {
          display: none !important;
        }
      }
    }

    @include respond-to(sm) {
      /* 在小屏幕上只显示最重要的列 */
      .el-table__column:nth-child(n + 3) {
        display: none !important;
      }

      /* 操作列只保留一个主要按钮 */
      .table-operation {
        .el-button:not(:first-child) {
          display: none !important;
        }
      }
    }
  }

  /* 搜索表单的响应式优化 */
  :deep(.table-search) {
    @include responsive-form;

    @include respond-to(md) {
      .el-form-item {
        .el-form-item__content {
          .el-input,
          .el-select,
          .el-date-picker {
            width: 100% !important;
          }
        }
      }
    }
  }

  /* 表格工具栏的响应式优化 */
  :deep(.table-header) {
    @include respond-to(md) {
      flex-direction: column !important;
      gap: var(--spacing-sm) !important;
      .table-title {
        text-align: center !important;
      }
      .table-operation {
        flex-wrap: wrap !important;
        justify-content: center !important;
        .el-button {
          margin: 2px !important;
          font-size: var(--font-size-xs) !important;
        }
      }
    }
  }
}
