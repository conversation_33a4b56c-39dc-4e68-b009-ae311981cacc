<template>
  <div v-if="showDebugger" class="responsive-debugger">
    <div class="debugger-header">
      <span class="debugger-title">响应式调试器</span>
      <el-button size="small" @click="toggleDebugger">
        {{ collapsed ? "展开" : "收起" }}
      </el-button>
    </div>
    
    <div v-show="!collapsed" class="debugger-content">
      <div class="info-grid">
        <div class="info-item">
          <span class="label">屏幕宽度:</span>
          <span class="value">{{ responsiveInfo.screenWidth }}px</span>
        </div>
        
        <div class="info-item">
          <span class="label">屏幕高度:</span>
          <span class="value">{{ responsiveInfo.screenHeight }}px</span>
        </div>
        
        <div class="info-item">
          <span class="label">当前断点:</span>
          <span class="value breakpoint" :class="responsiveInfo.breakpoint">
            {{ responsiveInfo.breakpoint.toUpperCase() }}
          </span>
        </div>
        
        <div class="info-item">
          <span class="label">设备类型:</span>
          <span class="value">
            {{ responsiveInfo.isMobile ? "移动设备" : 
               responsiveInfo.isTablet ? "平板设备" : 
               responsiveInfo.isDesktop ? "桌面设备" : "大屏设备" }}
          </span>
        </div>
        
        <div class="info-item">
          <span class="label">屏幕方向:</span>
          <span class="value">{{ responsiveInfo.orientation === "landscape" ? "横屏" : "竖屏" }}</span>
        </div>
        
        <div class="info-item">
          <span class="label">设备像素比:</span>
          <span class="value">{{ responsiveInfo.devicePixelRatio }}</span>
        </div>
        
        <div class="info-item">
          <span class="label">触摸设备:</span>
          <span class="value">{{ responsiveInfo.isTouchDevice ? "是" : "否" }}</span>
        </div>
      </div>
      
      <div class="breakpoint-indicators">
        <div class="indicator-title">断点状态:</div>
        <div class="indicators">
          <span class="indicator" :class="{ active: responsiveInfo.isXs }">XS</span>
          <span class="indicator" :class="{ active: responsiveInfo.isSm }">SM</span>
          <span class="indicator" :class="{ active: responsiveInfo.isMd }">MD</span>
          <span class="indicator" :class="{ active: responsiveInfo.isLg }">LG</span>
          <span class="indicator" :class="{ active: responsiveInfo.isXl }">XL</span>
          <span class="indicator" :class="{ active: responsiveInfo.isXxl }">XXL</span>
        </div>
      </div>
      
      <div class="container-size">
        <div class="size-title">建议容器样式:</div>
        <div class="size-info">
          <code>{{ JSON.stringify(containerSize, null, 2) }}</code>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useResponsive } from "@/hooks/useResponsive";

interface Props {
  show?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  show: false
});

const { responsiveInfo, getContainerSize } = useResponsive();

const showDebugger = ref(props.show);
const collapsed = ref(false);

const containerSize = computed(() => getContainerSize());

const toggleDebugger = () => {
  collapsed.value = !collapsed.value;
};

// 暴露方法供外部调用
defineExpose({
  show: () => { showDebugger.value = true; },
  hide: () => { showDebugger.value = false; },
  toggle: () => { showDebugger.value = !showDebugger.value; }
});
</script>

<style scoped lang="scss">
.responsive-debugger {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 9999;
  min-width: 280px;
  max-width: 400px;
  font-size: 12px;
  
  .dark & {
    background: rgba(30, 30, 30, 0.95);
    border-color: var(--el-border-color-dark);
  }
  
  .debugger-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    background: var(--el-bg-color-page);
    border-radius: 8px 8px 0 0;
    
    .debugger-title {
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }
  
  .debugger-content {
    padding: 12px;
    
    .info-grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: 6px;
      margin-bottom: 12px;
      
      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .label {
          color: var(--el-text-color-secondary);
          font-weight: 500;
        }
        
        .value {
          color: var(--el-text-color-primary);
          font-weight: 600;
          
          &.breakpoint {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            
            &.xs { background: #f56565; color: white; }
            &.sm { background: #ed8936; color: white; }
            &.md { background: #ecc94b; color: white; }
            &.lg { background: #48bb78; color: white; }
            &.xl { background: #4299e1; color: white; }
            &.xxl { background: #9f7aea; color: white; }
          }
        }
      }
    }
    
    .breakpoint-indicators {
      margin-bottom: 12px;
      
      .indicator-title {
        color: var(--el-text-color-secondary);
        font-weight: 500;
        margin-bottom: 6px;
      }
      
      .indicators {
        display: flex;
        gap: 4px;
        
        .indicator {
          padding: 2px 6px;
          border-radius: 4px;
          font-size: 10px;
          font-weight: 600;
          background: var(--el-fill-color-light);
          color: var(--el-text-color-secondary);
          transition: all 0.2s;
          
          &.active {
            background: var(--el-color-primary);
            color: white;
            transform: scale(1.1);
          }
        }
      }
    }
    
    .container-size {
      .size-title {
        color: var(--el-text-color-secondary);
        font-weight: 500;
        margin-bottom: 6px;
      }
      
      .size-info {
        background: var(--el-fill-color-extra-light);
        padding: 8px;
        border-radius: 4px;
        
        code {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 10px;
          color: var(--el-text-color-primary);
          white-space: pre-wrap;
        }
      }
    }
  }
}

/* 移动设备上的调试器优化 */
@media screen and (max-width: 767px) {
  .responsive-debugger {
    top: 10px;
    right: 10px;
    left: 10px;
    min-width: auto;
    max-width: none;
    
    .debugger-content {
      padding: 8px;
      
      .info-grid {
        .info-item {
          flex-direction: column;
          align-items: flex-start;
          gap: 2px;
        }
      }
    }
  }
}
</style>
