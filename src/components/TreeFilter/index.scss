@import "@/styles/mixins/responsive.scss";

.filter {
  box-sizing: border-box;
  flex-shrink: 0;
  width: 220px;
  min-width: 220px;
  height: 100%;
  padding: 18px;
  margin-right: 10px;

  /* 响应式宽度调整 */
  @include respond-between(992px, 1199px) {
    width: 200px;
    min-width: 200px;
    padding: 16px;
  }

  @include respond-between(768px, 991px) {
    width: 180px;
    min-width: 180px;
    padding: 14px;
    margin-right: 8px;
  }

  @include respond-to(md) {
    width: 100% !important;
    min-width: auto !important;
    height: auto !important;
    margin-right: 0 !important;
    margin-bottom: var(--spacing-md) !important;
    padding: 12px;
  }
  .title {
    margin: 0 0 15px;
    font-size: 18px;
    font-weight: bold;
    color: var(--el-color-info-dark-2);
    letter-spacing: 0.5px;

    @include respond-to(lg) {
      font-size: 16px;
      margin: 0 0 12px;
    }

    @include respond-to(md) {
      font-size: 15px;
      margin: 0 0 10px;
      text-align: center;
    }

    @include respond-to(sm) {
      font-size: 14px;
      margin: 0 0 8px;
    }
  }

  .search {
    display: flex;
    align-items: center;
    margin: 0 0 15px;

    @include respond-to(lg) {
      margin: 0 0 12px;
    }

    @include respond-to(md) {
      margin: 0 0 10px;
    }

    @include respond-to(sm) {
      margin: 0 0 8px;
    }

    .el-icon {
      cursor: pointer;
      transform: rotate(90deg) translateY(-8px);

      @include respond-to(sm) {
        transform: rotate(90deg) translateY(-6px);
      }
    }

    .el-input {
      @include respond-to(md) {
        .el-input__wrapper {
          font-size: var(--font-size-sm);
        }
      }

      @include respond-to(sm) {
        .el-input__wrapper {
          font-size: var(--font-size-xs);
        }
      }
    }
  }
  .el-scrollbar {
    :deep(.el-tree) {
      height: 80%;
      overflow: auto;

      @include respond-to(md) {
        height: 200px; /* 在移动设备上设置固定高度 */
      }

      @include respond-to(sm) {
        height: 150px;
      }

      .el-tree-node__content {
        height: 33px;

        @include respond-to(lg) {
          height: 30px;
        }

        @include respond-to(md) {
          height: 28px;
        }

        @include respond-to(sm) {
          height: 26px;
        }
      }

      .el-tree-node__label {
        width: 150px;
        margin-right: 33px;
        overflow: hidden;
        text-align: center;
        text-overflow: ellipsis;
        white-space: nowrap;

        @include respond-to(lg) {
          width: 130px;
          margin-right: 20px;
          font-size: 14px;
        }

        @include respond-to(md) {
          width: 100%;
          margin-right: 10px;
          font-size: 13px;
          text-align: left;
        }

        @include respond-to(sm) {
          font-size: 12px;
        }
      }

      .el-tree-node__expand-icon {
        @include respond-to(sm) {
          font-size: 12px;
        }
      }
    }

    :deep(.el-tree--highlight-current) {
      .el-tree-node.is-current > .el-tree-node__content {
        background-color: color-mix(in srgb, var(--el-color-primary) 80%, transparent);
        backdrop-filter: blur(10px);
        border-radius: 5px;

        @include respond-to(md) {
          border-radius: 4px;
        }

        .el-tree-node__label,
        .el-tree-node__expand-icon {
          color: white;
        }

        .is-leaf {
          color: transparent;
        }
      }
    }
  }

  /* 移动设备上的下拉菜单优化 */
  @include respond-to(md) {
    :deep(.el-dropdown) {
      width: 100%;

      .el-dropdown-link {
        width: 100%;
        justify-content: center;
      }
    }

    :deep(.el-dropdown-menu) {
      min-width: 140px;

      .el-dropdown-menu__item {
        padding: 8px 12px;
        font-size: var(--font-size-sm);
      }
    }
  }
}
