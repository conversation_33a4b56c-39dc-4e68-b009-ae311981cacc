import { ref, computed, onMounted, onUnmounted, readonly, watch } from "vue";
import { useDebounceFn } from "@vueuse/core";
import { useGlobalStore } from "@/stores/modules/global";

export interface ResponsiveInfo {
  screenWidth: number;
  screenHeight: number;
  isXs: boolean;
  isSm: boolean;
  isMd: boolean;
  isLg: boolean;
  isXl: boolean;
  isXxl: boolean;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isLarge: boolean;
  breakpoint: "xs" | "sm" | "md" | "lg" | "xl" | "xxl";
  orientation: "portrait" | "landscape";
  devicePixelRatio: number;
}

export interface ResponsiveConfig {
  autoCollapseSidebar?: boolean;
  mobileBreakpoint?: number;
  tabletBreakpoint?: number;
  desktopBreakpoint?: number;
}

/**
 * 增强的响应式布局Hook
 * 提供全面的屏幕尺寸检测、设备类型判断和响应式状态管理
 */
export const useResponsive = (config: ResponsiveConfig = {}) => {
  const globalStore = useGlobalStore();
  const screenWidth = ref(0);
  const screenHeight = ref(0);
  const devicePixelRatio = ref(1);

  // 断点定义 - 与CSS变量保持一致
  const breakpoints = {
    xs: 480,
    sm: 576,
    md: 768,
    lg: 992,
    xl: 1200,
    xxl: 1440
  };

  // 配置默认值
  const {
    autoCollapseSidebar = true,
    mobileBreakpoint = breakpoints.md,
    tabletBreakpoint = breakpoints.lg,
    desktopBreakpoint = breakpoints.xl
  } = config;

  // 计算各种断点状态
  const isXs = computed(() => screenWidth.value < breakpoints.xs);
  const isSm = computed(() => screenWidth.value >= breakpoints.xs && screenWidth.value < breakpoints.sm);
  const isMd = computed(() => screenWidth.value >= breakpoints.sm && screenWidth.value < breakpoints.md);
  const isLg = computed(() => screenWidth.value >= breakpoints.md && screenWidth.value < breakpoints.lg);
  const isXl = computed(() => screenWidth.value >= breakpoints.lg && screenWidth.value < breakpoints.xl);
  const isXxl = computed(() => screenWidth.value >= breakpoints.xl);

  // 计算设备类型（兼容旧版本）
  const isMobile = computed(() => screenWidth.value < mobileBreakpoint);
  const isTablet = computed(() => screenWidth.value >= mobileBreakpoint && screenWidth.value < tabletBreakpoint);
  const isDesktop = computed(() => screenWidth.value >= tabletBreakpoint && screenWidth.value < desktopBreakpoint);
  const isLarge = computed(() => screenWidth.value >= desktopBreakpoint);

  // 屏幕方向
  const orientation = computed(() => {
    return screenWidth.value > screenHeight.value ? "landscape" : "portrait";
  });

  // 当前断点
  const breakpoint = computed((): ResponsiveInfo["breakpoint"] => {
    if (isXs.value) return "xs";
    if (isSm.value) return "sm";
    if (isMd.value) return "md";
    if (isLg.value) return "lg";
    if (isXl.value) return "xl";
    return "xxl";
  });

  // 响应式信息对象
  const responsiveInfo = computed(
    (): ResponsiveInfo => ({
      screenWidth: screenWidth.value,
      screenHeight: screenHeight.value,
      isXs: isXs.value,
      isSm: isSm.value,
      isMd: isMd.value,
      isLg: isLg.value,
      isXl: isXl.value,
      isXxl: isXxl.value,
      isMobile: isMobile.value,
      isTablet: isTablet.value,
      isDesktop: isDesktop.value,
      isLarge: isLarge.value,
      breakpoint: breakpoint.value,
      orientation: orientation.value,
      devicePixelRatio: devicePixelRatio.value
    })
  );

  // 更新屏幕尺寸和设备信息
  const updateScreenInfo = () => {
    screenWidth.value = window.innerWidth;
    screenHeight.value = window.innerHeight;
    devicePixelRatio.value = window.devicePixelRatio || 1;
  };

  // 自动调整侧边栏状态
  const adjustSidebarCollapse = () => {
    if (autoCollapseSidebar && globalStore) {
      if (isMobile.value) {
        // 移动设备自动折叠侧边栏
        globalStore.setGlobalState("isCollapse", true);
      }
      // 注意：这里不自动展开，保持用户的选择
    }
  };

  // 防抖的resize处理器
  const debouncedResize = useDebounceFn(() => {
    updateScreenInfo();
    adjustSidebarCollapse();
  }, 100);

  // 监听断点变化
  watch(
    () => breakpoint.value,
    (newBreakpoint, oldBreakpoint) => {
      if (newBreakpoint !== oldBreakpoint) {
        // 断点变化时的回调
        adjustSidebarCollapse();
      }
    }
  );

  // 获取响应式类名
  const getResponsiveClass = () => {
    const classes = [];
    if (isXs.value) classes.push("is-xs");
    if (isSm.value) classes.push("is-sm");
    if (isMd.value) classes.push("is-md");
    if (isLg.value) classes.push("is-lg");
    if (isXl.value) classes.push("is-xl");
    if (isXxl.value) classes.push("is-xxl");
    if (isMobile.value) classes.push("is-mobile");
    if (isTablet.value) classes.push("is-tablet");
    if (isDesktop.value) classes.push("is-desktop");
    classes.push(`is-${orientation.value}`);
    return classes.join(" ");
  };

  // 判断是否为触摸设备
  const isTouchDevice = computed(() => {
    return "ontouchstart" in window || navigator.maxTouchPoints > 0;
  });

  // 获取容器尺寸建议
  const getContainerSize = () => {
    if (isXs.value) return { padding: "4px", gap: "4px", fontSize: "11px" };
    if (isSm.value) return { padding: "8px", gap: "8px", fontSize: "12px" };
    if (isMd.value) return { padding: "12px", gap: "12px", fontSize: "14px" };
    if (isLg.value) return { padding: "16px", gap: "16px", fontSize: "14px" };
    if (isXl.value) return { padding: "20px", gap: "20px", fontSize: "16px" };
    return { padding: "24px", gap: "24px", fontSize: "16px" };
  };

  // 初始化
  onMounted(() => {
    updateScreenInfo();
    adjustSidebarCollapse();
    window.addEventListener("resize", debouncedResize);
    window.addEventListener("orientationchange", debouncedResize);
  });

  onUnmounted(() => {
    window.removeEventListener("resize", debouncedResize);
    window.removeEventListener("orientationchange", debouncedResize);
  });

  return {
    // 屏幕尺寸信息
    screenWidth: readonly(screenWidth),
    screenHeight: readonly(screenHeight),
    devicePixelRatio: readonly(devicePixelRatio),

    // 断点状态
    isXs: readonly(isXs),
    isSm: readonly(isSm),
    isMd: readonly(isMd),
    isLg: readonly(isLg),
    isXl: readonly(isXl),
    isXxl: readonly(isXxl),

    // 设备类型（兼容旧版本）
    isMobile: readonly(isMobile),
    isTablet: readonly(isTablet),
    isDesktop: readonly(isDesktop),
    isLarge: readonly(isLarge),

    // 其他信息
    breakpoint: readonly(breakpoint),
    orientation: readonly(orientation),
    isTouchDevice: readonly(isTouchDevice),

    // 完整响应式信息对象
    responsiveInfo: readonly(responsiveInfo),

    // 工具函数
    getResponsiveClass,
    getContainerSize,
    updateScreenInfo,

    // 配置和常量
    breakpoints,
    config: readonly(ref(config))
  };
};

/**
 * 移动端菜单控制Hook
 */
export const useMobileMenu = () => {
  const showMobileMenu = ref(false);

  const toggleMobileMenu = () => {
    showMobileMenu.value = !showMobileMenu.value;
  };

  const openMobileMenu = () => {
    showMobileMenu.value = true;
  };

  const closeMobileMenu = () => {
    showMobileMenu.value = false;
  };

  return {
    showMobileMenu: readonly(showMobileMenu),
    toggleMobileMenu,
    openMobileMenu,
    closeMobileMenu
  };
};

/**
 * 设备类型检测Hook
 */
export const useDeviceDetection = () => {
  const { responsiveInfo } = useResponsive();

  // 设备类型判断
  const deviceType = computed(() => {
    const { isMobile, isTablet } = responsiveInfo.value;
    if (isMobile) return "mobile";
    if (isTablet) return "tablet";
    return "desktop";
  });

  // 是否为触摸设备 (复用主Hook中的定义)
  const { isTouchDevice: mainIsTouchDevice } = useResponsive();
  const isTouchDevice = mainIsTouchDevice;

  // 获取设备信息
  const getDeviceInfo = () => {
    const userAgent = navigator.userAgent.toLowerCase();
    const isMobileDevice = /mobile|android|iphone|ipod|phone|blackberry|iemobile|opera mini/.test(userAgent);
    const isTabletDevice = /ipad|tablet|playbook|silk/.test(userAgent);

    return {
      userAgent,
      isMobileDevice,
      isTabletDevice,
      isPCDevice: !isMobileDevice && !isTabletDevice,
      isTouchDevice: isTouchDevice.value,
      deviceType: deviceType.value
    };
  };

  return {
    deviceType: readonly(deviceType),
    isTouchDevice: readonly(isTouchDevice),
    getDeviceInfo
  };
};
