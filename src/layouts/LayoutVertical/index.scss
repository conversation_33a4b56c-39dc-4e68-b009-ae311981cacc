@import "@/styles/mixins/responsive.scss";

.el-container {
  width: 100%;
  height: 100%;

  :deep(.el-aside) {
    width: auto;
    background-color: var(--el-menu-bg-color);
    border-right: 1px solid var(--el-aside-border-color);

    .aside-box {
      display: flex;
      flex-direction: column;
      height: 100%;
      transition: width 0.3s ease;

      /* 响应式侧边栏宽度 */
      @include respond-above(xxl) {
        width: 240px;
      }

      @include respond-between(1200px, 1439px) {
        width: 210px;
      }

      @include respond-between(992px, 1199px) {
        width: 200px;
      }

      @include respond-between(768px, 991px) {
        width: 180px;
      }

      @include respond-to(md) {
        width: 64px;

        .logo-text {
          display: none;
        }

        .el-menu {
          .el-menu-item,
          .el-sub-menu__title {
            padding: 0 16px !important;

            .el-menu-item__title,
            .el-sub-menu__title {
              display: none;
            }
          }
        }
      }

      .el-scrollbar {
        height: calc(100% - 55px);

        @include respond-to(lg) {
          height: calc(100% - 50px);
        }

        @include respond-to(md) {
          height: calc(100% - 48px);
        }

        @include respond-to(sm) {
          height: calc(100% - 44px);
        }

        .el-menu {
          width: 100%;
          overflow-x: hidden;
          border-right: none;
        }
      }

      .logo {
        box-sizing: border-box;
        height: 55px;

        @include respond-to(lg) {
          height: 50px;
        }

        @include respond-to(md) {
          height: 48px;
        }

        @include respond-to(sm) {
          height: 44px;
        }

        .logo-img {
          width: 28px;
          height: 28px;
          object-fit: contain;

          @include respond-to(md) {
            width: 24px;
            height: 24px;
          }

          @include respond-to(sm) {
            width: 20px;
            height: 20px;
          }
        }

        .logo-text {
          margin-left: 6px;
          font-size: 21.5px;
          font-weight: bold;
          color: var(--el-aside-logo-text-color);
          white-space: nowrap;

          @include respond-between(768px, 991px) {
            font-size: 18px;
          }

          @include respond-to(md) {
            display: none;
          }
        }
      }
    }
  }

  .el-header {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 55px;
    padding: 0 15px;
    background-color: var(--el-header-bg-color);
    border-bottom: 1px solid var(--el-header-border-color);

    /* 响应式头部高度和间距 */
    @include respond-above(xxl) {
      padding: 0 32px;
    }

    @include respond-between(992px, 1199px) {
      padding: 0 20px;
    }

    @include respond-between(768px, 991px) {
      padding: 0 16px;
      height: 50px;
    }

    @include respond-to(md) {
      padding: 0 10px;
      height: 48px;
    }

    @include respond-to(sm) {
      padding: 0 8px;
      height: 44px;
    }
  }
}
