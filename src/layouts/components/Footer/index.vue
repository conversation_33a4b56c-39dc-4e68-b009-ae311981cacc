<template>
  <div class="footer">
    <div class="footer-content">
      <div class="footer-left">
        <a
          class="beian-link"
          href="https://beian.miit.gov.cn"
          target="_blank"
          rel="noopener noreferrer"
          :title="$t('footer.beianLink')"
        >
          <i class="iconfont icon-beian"></i>
          {{ $t("footer.beianNumber") }}
        </a>
      </div>
      <div class="footer-right">
        <span class="copyright">
          {{ $t("footer.copyright") }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页尾组件不需要引入useI18n，因为模板中直接使用了$t()
</script>

<style scoped lang="scss">
.footer {
  height: 50px;
  padding: 0 20px;
  background: linear-gradient(135deg, var(--el-bg-color) 0%, var(--el-bg-color-page) 100%);
  border-top: 1px solid var(--el-border-color-light);
  box-shadow: 0 -2px 8px rgb(0 0 0 / 4%);
  transition: all 0.3s ease;
  .footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px;
    height: 100%;
    margin: 0 auto;
  }
  .footer-left {
    display: flex;
    align-items: center;
    .beian-link {
      display: flex;
      gap: 6px;
      align-items: center;
      font-size: 13px;
      color: var(--el-text-color-secondary);
      text-decoration: none;
      letter-spacing: 0.3px;
      transition: all 0.3s ease;
      .iconfont {
        font-size: 14px;
        opacity: 0.8;
      }
      &:hover {
        color: var(--el-color-primary);
        transform: translateX(2px);
        .iconfont {
          opacity: 1;
          transform: scale(1.1);
        }
      }
    }
  }
  .footer-right {
    .copyright {
      font-size: 13px;
      color: var(--el-text-color-regular);
      letter-spacing: 0.2px;
      opacity: 0.8;
      transition: opacity 0.3s ease;
      &:hover {
        opacity: 1;
      }
    }
  }

  // 响应式设计
  @media screen and (width <= 768px) {
    height: auto;
    padding: 12px 16px;
    .footer-content {
      flex-direction: column;
      gap: 8px;
      text-align: center;
    }
    .footer-left,
    .footer-right {
      width: 100%;
    }
    .beian-link,
    .copyright {
      font-size: 12px !important;
    }
  }

  @media screen and (width <= 480px) {
    padding: 10px 12px;
    .beian-link,
    .copyright {
      font-size: 11px !important;
    }
  }

  // 暗色主题适配
  .dark & {
    background: linear-gradient(135deg, var(--el-bg-color) 0%, rgb(0 0 0 / 10%) 100%);
    border-top-color: var(--el-border-color);
    box-shadow: 0 -2px 8px rgb(0 0 0 / 15%);
  }
}
</style>
