<template>
  <div class="tool-bar-lf">
    <CollapseIcon id="collapseIcon" />
    <Breadcrumb v-show="globalStore.breadcrumb" id="breadcrumb" />
  </div>
</template>

<script setup lang="ts">
import { useGlobalStore } from "@/stores/modules/global";
import CollapseIcon from "./components/CollapseIcon.vue";
import Breadcrumb from "./components/Breadcrumb.vue";
const globalStore = useGlobalStore();
</script>

<style scoped lang="scss">
@import "@/styles/mixins/responsive";
.tool-bar-lf {
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: flex-start;
  overflow: hidden;
  white-space: nowrap;

  @include respond-to(lg) {
    gap: 8px;
  }

  @include respond-to(md) {
    gap: 6px;
  }

  @include respond-to(sm) {
    gap: 4px;
  }

  /* 面包屑在移动设备上的处理 */
  #breadcrumb {
    @include respond-to(md) {
      display: none !important;
    }
  }

  /* 折叠图标的响应式调整 */
  #collapseIcon {
    @include respond-to(sm) {
      :deep(.toolBar-icon) {
        padding: 6px;
        font-size: 16px;
      }
    }
  }
}
</style>
