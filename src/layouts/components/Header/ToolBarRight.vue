<template>
  <div class="tool-bar-ri">
    <div class="header-icon">
      <AssemblySize id="assemblySize" />
      <Language id="language" />
      <SearchMenu id="searchMenu" />
      <DarkMode id="darkMode" />
      <ThemeSetting id="themeSetting" />
      <Message id="message" />
      <Fullscreen id="fullscreen" />
    </div>
    <span class="username">{{ username }}</span>
    <Avatar />
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useUserStore } from "@/stores/modules/user";
import AssemblySize from "./components/AssemblySize.vue";
import Language from "./components/Language.vue";
import SearchMenu from "./components/SearchMenu.vue";
import DarkMode from "./components/DarkMode.vue";
import ThemeSetting from "./components/ThemeSetting.vue";
import Message from "./components/Message.vue";
import Fullscreen from "./components/Fullscreen.vue";
import Avatar from "./components/Avatar.vue";

const userStore = useUserStore();
const username = computed(() => userStore.userInfo.name);
</script>

<style scoped lang="scss">
@import "@/styles/mixins/responsive";
.tool-bar-ri {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 25px;

  @include respond-to(lg) {
    padding-right: 20px;
  }

  @include respond-to(md) {
    padding-right: 16px;
  }

  @include respond-to(sm) {
    padding-right: 8px;
  }
  .header-icon {
    display: flex;
    align-items: center;
    & > * {
      margin-left: 21px;
      color: var(--el-header-text-color);

      @include respond-to(lg) {
        margin-left: 16px;
      }

      @include respond-to(md) {
        margin-left: 12px;
      }

      @include respond-to(sm) {
        margin-left: 8px;
      }
    }

    /* 在移动设备上隐藏部分图标 */
    @include respond-to(md) {
      #assemblySize,
      #searchMenu,
      #themeSetting,
      #fullscreen {
        display: none;
      }
    }

    @include respond-to(sm) {
      #language,
      #message {
        display: none;
      }
    }
  }
  .username {
    margin: 0 20px;
    font-size: 15px;
    color: var(--el-header-text-color);

    @include respond-to(lg) {
      margin: 0 16px;
      font-size: 14px;
    }

    @include respond-to(md) {
      margin: 0 12px;
      font-size: 13px;
    }

    @include respond-to(sm) {
      display: none; /* 在小屏幕上隐藏用户名 */
    }
  }
}

// 统一的工具栏图标hover效果
:deep(.toolBar-icon) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  font-size: 18px;
  color: var(--el-header-text-color);
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

  @include respond-to(lg) {
    padding: 7px;
    font-size: 17px;
  }

  @include respond-to(md) {
    padding: 6px;
    font-size: 16px;
  }

  @include respond-to(sm) {
    padding: 5px;
    font-size: 15px;
  }
  &:hover {
    color: var(--el-color-primary);
    background-color: var(--el-color-primary-light-9);
    box-shadow: 0 4px 12px rgb(64 158 255 / 20%);
    transform: scale(1.1);

    @include respond-to(sm) {
      transform: scale(1.05); /* 在小屏幕上减少缩放效果 */
    }
  }
  &:active {
    transform: scale(0.95);

    @include respond-to(sm) {
      transform: scale(0.98);
    }
  }
}
</style>
