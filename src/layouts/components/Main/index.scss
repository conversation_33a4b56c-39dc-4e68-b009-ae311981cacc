@import "@/styles/mixins/responsive.scss";

.table-box {
  @include responsive-spacing(padding, 20px);
  background-color: var(--el-bg-color);
  border-radius: 16px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
  transition: all 0.3s ease;

  @include respond-to(md) {
    border-radius: 12px;
  }

  @include respond-to(sm) {
    border-radius: 8px;
    box-shadow: 0 1px 8px 0 rgb(0 0 0 / 5%);
  }

  &:hover {
    box-shadow: 0 4px 16px 0 rgb(0 0 0 / 10%);

    @include respond-to(sm) {
      box-shadow: 0 2px 12px 0 rgb(0 0 0 / 8%);
    }
  }
  :deep(.el-table) {
    @include responsive-table;
    overflow: hidden;
    border-radius: 8px;

    @include respond-to(md) {
      border-radius: 6px;
    }

    @include respond-to(sm) {
      border-radius: 4px;
    }

    th {
      font-weight: 600;
      color: var(--el-text-color-primary);
      background-color: var(--el-bg-color-page);

      @include respond-to(lg) {
        padding: 10px 0;
        font-size: 14px;
      }

      @include respond-to(md) {
        padding: 8px 0;
        font-size: 13px;
      }

      @include respond-to(sm) {
        padding: 6px 4px;
        font-size: 12px;
      }
    }

    td {
      padding: 12px 0;

      @include respond-to(lg) {
        padding: 10px 0;
      }

      @include respond-to(md) {
        padding: 8px 0;
      }

      @include respond-to(sm) {
        padding: 6px 4px;
      }
    }

    .el-table__row {
      transition: all 0.3s ease;
      &:hover {
        background-color: var(--el-fill-color-light);
      }
    }

    /* 移动设备上的表格优化 */
    @include respond-to(md) {
      .el-table__header,
      .el-table__body {
        font-size: var(--font-size-xs);
      }

      /* 隐藏不重要的列 */
      .el-table__column:nth-child(n+4) {
        display: none;
      }
    }

    @include respond-to(sm) {
      .el-table__column:nth-child(n+3) {
        display: none;
      }
    }
  }
  :deep(.el-pagination) {
    justify-content: flex-end;
    padding: 0 20px;
    margin-top: 20px;

    @include respond-to(lg) {
      padding: 0 16px;
      margin-top: 16px;
    }

    @include respond-to(md) {
      justify-content: center;
      padding: 0 12px;
      margin-top: 12px;
      flex-wrap: wrap;

      .el-pagination__total,
      .el-pagination__jump {
        display: none;
      }

      .el-pager {
        .number {
          min-width: 28px;
          height: 28px;
          line-height: 28px;
          font-size: var(--font-size-xs);
        }
      }
    }

    @include respond-to(sm) {
      padding: 0 8px;
      margin-top: 8px;

      .el-pager {
        .number {
          min-width: 24px;
          height: 24px;
          line-height: 24px;
          font-size: 11px;
        }
      }

      .el-pagination__prev,
      .el-pagination__next {
        padding: 0 8px;
        font-size: 12px;
      }
    }
  }
  :deep(.el-input__wrapper) {
    box-shadow: 0 0 0 1px var(--el-border-color) inset;
    transition: all 0.3s ease;
    &:hover {
      box-shadow: 0 0 0 1px var(--el-border-color-hover) inset;
    }
    &.is-focus {
      box-shadow: 0 0 0 1px var(--el-color-primary) inset;
    }
  }
  :deep(.el-button) {
    transition: all 0.3s ease;
    &.view-button {
      &:hover {
        box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
        transform: translateY(-1px);
      }
    }
  }
  :deep(.el-table__header-wrapper) {
    th {
      font-weight: 600;
      color: var(--el-text-color-primary);
      background-color: var(--el-bg-color-page);
    }
  }
  :deep(.el-table__body-wrapper) {
    td {
      color: var(--el-text-color-regular);
    }
  }
}
.el-footer {
  height: auto;
  padding: 0;
}
