/* flex */
.flx-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flx-justify-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flx-align-center {
  display: flex;
  align-items: center;
}

/* clearfix */
.clearfix::after {
  display: block;
  height: 0;
  overflow: hidden;
  clear: both;
  content: "";
}

/* 文字单行省略号 */
.sle {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 文字多行省略号 */
.mle {
  display: -webkit-box;
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

/* 文字多了自动換行 */
.break-word {
  word-break: break-all;
  word-wrap: break-word;
}

/* fade-transform */
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all 0.2s;
}
.fade-transform-enter-from {
  opacity: 0;
  transition: all 0.2s;
  transform: translateX(-30px);
}
.fade-transform-leave-to {
  opacity: 0;
  transition: all 0.2s;
  transform: translateX(30px);
}

/* 引入毛玻璃效果样式 */
@import "./glassmorphism";

/* 引入响应式Mixin */
@import "./mixins/responsive";

/* 引入Element Plus响应式样式 */
@import "./element-responsive";

/* 引入Drawer专用响应式样式 */
@import "./drawer-responsive.scss";

/* 引入移动端表格优化样式 */
@import "./mobile-table.scss";

/* 引入异地组网页面响应式样式 */
@import "./rnet-responsive.scss";

/* 引入异地组网页面强制布局样式 */
@import "./rnet-force-layout.scss";

/* breadcrumb-transform */
.breadcrumb-enter-active {
  transition: all 0.2s;
}
.breadcrumb-enter-from,
.breadcrumb-leave-active {
  opacity: 0;
  transform: translateX(10px);
}

/* scroll bar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar-thumb {
  background-color: var(--el-border-color-darker);
  border-radius: 20px;
}

/* nprogress */
#nprogress .bar {
  background: var(--el-color-primary) !important;
}
#nprogress .spinner-icon {
  border-top-color: var(--el-color-primary) !important;
  border-left-color: var(--el-color-primary) !important;
}
#nprogress .peg {
  box-shadow:
    0 0 10px var(--el-color-primary),
    0 0 5px var(--el-color-primary) !important;
}

/* 外边距、内边距全局样式 */
@for $i from 0 through 100 {
  .mt#{$i} {
    margin-top: #{$i}px !important;
  }
  .mr#{$i} {
    margin-right: #{$i}px !important;
  }
  .mb#{$i} {
    margin-bottom: #{$i}px !important;
  }
  .ml#{$i} {
    margin-left: #{$i}px !important;
  }
  .pt#{$i} {
    padding-top: #{$i}px !important;
  }
  .pr#{$i} {
    padding-right: #{$i}px !important;
  }
  .pb#{$i} {
    padding-bottom: #{$i}px !important;
  }
  .pl#{$i} {
    padding-left: #{$i}px !important;
  }
}
.table-header {
  .cell {
    display: inline-block;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
