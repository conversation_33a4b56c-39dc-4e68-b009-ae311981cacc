/* 全局毛玻璃效果样式 */

// 基础毛玻璃效果
.glass-effect {
  background: rgb(255 255 255 / 10%) !important;
  backdrop-filter: blur(10px) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgb(255 255 255 / 18%) !important;
  box-shadow: 0 8px 32px rgb(0 0 0 / 10%) !important;
}

// 卡片毛玻璃效果
.glass-card {
  background: rgb(255 255 255 / 10%) !important;
  backdrop-filter: blur(10px) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgb(255 255 255 / 18%) !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgb(0 0 0 / 10%) !important;
  transition: all 0.3s ease !important;
  &:hover {
    background: rgb(255 255 255 / 20%) !important;
    box-shadow: 0 12px 32px rgb(0 0 0 / 15%) !important;
    transform: translateY(-5px) !important;
  }
}

// 内容卡片毛玻璃效果
.glass-content-card {
  background: rgb(255 255 255 / 10%) !important;
  backdrop-filter: blur(8px) !important;
  backdrop-filter: blur(8px) !important;
  border: 1px solid rgb(255 255 255 / 18%) !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%) !important;
  transition: all 0.3s ease !important;
  &:hover {
    background: rgb(255 255 255 / 15%) !important;
    box-shadow: 0 8px 24px rgb(0 0 0 / 12%) !important;
    transform: translateY(-3px) !important;
  }
}

// 容器毛玻璃效果
.glass-container {
  position: relative !important;
  overflow: hidden !important;
  &::before {
    position: absolute !important;
    top: -100px !important;
    right: -100px !important;
    z-index: 0 !important;
    width: 300px !important;
    height: 300px !important;
    content: "" !important;
    background: radial-gradient(circle, rgb(255 255 255 / 80%) 0%, rgb(255 255 255 / 0%) 70%) !important;
    opacity: 0.4 !important;
  }
  &::after {
    position: absolute !important;
    bottom: -100px !important;
    left: -100px !important;
    z-index: 0 !important;
    width: 300px !important;
    height: 300px !important;
    content: "" !important;
    background: radial-gradient(circle, rgb(173 216 230 / 80%) 0%, rgb(173 216 230 / 0%) 70%) !important;
    opacity: 0.3 !important;
  }
}

// 背景渐变
.glass-bg {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
}

// 对话框毛玻璃效果
.glass-dialog {
  .el-dialog {
    overflow: hidden !important;
    background: rgb(255 255 255 / 70%) !important;
    backdrop-filter: blur(10px) !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgb(255 255 255 / 18%) !important;
    border-radius: 16px !important;
    box-shadow: 0 8px 32px rgb(0 0 0 / 10%) !important;
    .el-dialog__header {
      background: rgb(255 255 255 / 10%) !important;
      border-bottom: 1px solid rgb(255 255 255 / 20%) !important;
    }
    .el-dialog__body {
      background: rgb(255 255 255 / 5%) !important;
    }
    .el-dialog__footer {
      background: rgb(255 255 255 / 10%) !important;
      border-top: 1px solid rgb(255 255 255 / 20%) !important;
    }
  }
}

// 暗色模式适配
.dark {
  .glass-effect,
  .glass-card,
  .glass-content-card {
    background: rgb(30 30 30 / 30%) !important;
    border: 1px solid rgb(255 255 255 / 10%) !important;
  }
  .glass-card:hover {
    background: rgb(40 40 40 / 40%) !important;
    box-shadow: 0 12px 32px rgb(0 0 0 / 30%) !important;
  }
  .glass-content-card:hover {
    background: rgb(40 40 40 / 40%) !important;
    box-shadow: 0 8px 24px rgb(0 0 0 / 20%) !important;
  }
  .glass-bg {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%) !important;
  }
  .glass-container {
    &::before {
      background: radial-gradient(circle, rgb(70 130 180 / 30%) 0%, rgb(70 130 180 / 0%) 70%) !important;
    }
    &::after {
      background: radial-gradient(circle, rgb(255 100 100 / 30%) 0%, rgb(255 100 100 / 0%) 70%) !important;
    }
  }
  .glass-dialog .el-dialog {
    background: rgb(30 30 30 / 70%) !important;
    border: 1px solid rgb(255 255 255 / 10%) !important;
    .el-dialog__header {
      background: rgb(40 40 40 / 50%) !important;
      border-bottom: 1px solid rgb(255 255 255 / 10%) !important;
    }
    .el-dialog__body {
      background: rgb(30 30 30 / 30%) !important;
    }
    .el-dialog__footer {
      background: rgb(40 40 40 / 50%) !important;
      border-top: 1px solid rgb(255 255 255 / 10%) !important;
    }
  }
}
