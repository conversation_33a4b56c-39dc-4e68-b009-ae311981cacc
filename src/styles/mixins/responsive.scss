/* 响应式Mixin集合 */

/* ===== 断点Mixin ===== */
@mixin respond-to($breakpoint) {
  @if $breakpoint == xs {
    @media screen and (max-width: 479px) {
      @content;
    }
  }
  @if $breakpoint == sm {
    @media screen and (max-width: 575px) {
      @content;
    }
  }
  @if $breakpoint == md {
    @media screen and (max-width: 767px) {
      @content;
    }
  }
  @if $breakpoint == lg {
    @media screen and (max-width: 991px) {
      @content;
    }
  }
  @if $breakpoint == xl {
    @media screen and (max-width: 1199px) {
      @content;
    }
  }
  @if $breakpoint == xxl {
    @media screen and (min-width: 1440px) {
      @content;
    }
  }
}

@mixin respond-above($breakpoint) {
  @if $breakpoint == xs {
    @media screen and (min-width: 480px) {
      @content;
    }
  }
  @if $breakpoint == sm {
    @media screen and (min-width: 576px) {
      @content;
    }
  }
  @if $breakpoint == md {
    @media screen and (min-width: 768px) {
      @content;
    }
  }
  @if $breakpoint == lg {
    @media screen and (min-width: 992px) {
      @content;
    }
  }
  @if $breakpoint == xl {
    @media screen and (min-width: 1200px) {
      @content;
    }
  }
  @if $breakpoint == xxl {
    @media screen and (min-width: 1440px) {
      @content;
    }
  }
}

@mixin respond-between($min, $max) {
  @media screen and (min-width: $min) and (max-width: $max) {
    @content;
  }
}

/* ===== 容器响应式Mixin ===== */
@mixin responsive-container {
  padding: var(--container-padding-mobile);
  
  @include respond-above(md) {
    padding: var(--container-padding-tablet);
  }
  
  @include respond-above(xl) {
    padding: var(--container-padding-desktop);
  }
}

/* ===== 网格响应式Mixin ===== */
@mixin responsive-grid($mobile: 1, $tablet: 2, $desktop: 3, $large: 4) {
  display: grid;
  gap: var(--spacing-md);
  grid-template-columns: repeat($mobile, 1fr);
  
  @include respond-above(md) {
    grid-template-columns: repeat($tablet, 1fr);
  }
  
  @include respond-above(lg) {
    grid-template-columns: repeat($desktop, 1fr);
  }
  
  @include respond-above(xl) {
    grid-template-columns: repeat($large, 1fr);
  }
}

/* ===== Flex响应式Mixin ===== */
@mixin responsive-flex($direction: column) {
  display: flex;
  flex-direction: $direction;
  gap: var(--spacing-md);
  
  @include respond-above(md) {
    @if $direction == column {
      flex-direction: row;
    } @else {
      flex-direction: column;
    }
  }
}

/* ===== 字体响应式Mixin ===== */
@mixin responsive-font-size($base-size: 16px) {
  font-size: $base-size * 0.75;
  
  @include respond-above(sm) {
    font-size: $base-size * 0.875;
  }
  
  @include respond-above(md) {
    font-size: $base-size;
  }
  
  @include respond-above(lg) {
    font-size: $base-size * 1.125;
  }
  
  @include respond-above(xl) {
    font-size: $base-size * 1.25;
  }
}

/* ===== 间距响应式Mixin ===== */
@mixin responsive-spacing($property: padding, $base: 16px) {
  #{$property}: $base * 0.5;
  
  @include respond-above(md) {
    #{$property}: $base;
  }
  
  @include respond-above(lg) {
    #{$property}: $base * 1.25;
  }
  
  @include respond-above(xl) {
    #{$property}: $base * 1.5;
  }
}

/* ===== 表格响应式Mixin ===== */
@mixin responsive-table {
  width: 100%;
  overflow-x: auto;
  
  @include respond-to(md) {
    font-size: var(--font-size-xs);
    
    .el-table__header th,
    .el-table__body td {
      padding: 6px 4px !important;
    }
    
    /* 隐藏不重要的列 */
    .el-table__column:nth-child(n+4) {
      display: none;
    }
  }
  
  @include respond-to(sm) {
    .el-table__column:nth-child(n+3) {
      display: none;
    }
  }
}

/* ===== 表单响应式Mixin ===== */
@mixin responsive-form {
  .el-form-item {
    margin-bottom: var(--spacing-md);
    
    @include respond-to(md) {
      .el-form-item__label {
        display: block !important;
        text-align: left !important;
        margin-bottom: var(--spacing-xs) !important;
        font-size: var(--font-size-sm) !important;
      }
      
      .el-form-item__content {
        margin-left: 0 !important;
        
        .el-input,
        .el-select,
        .el-textarea {
          width: 100% !important;
        }
      }
    }
    
    @include respond-to(sm) {
      margin-bottom: 12px;
      
      .el-form-item__label {
        font-size: var(--font-size-xs) !important;
      }
    }
  }
  
  /* 按钮组响应式 */
  .el-form-item__content {
    .el-button-group,
    .form-buttons {
      @include respond-to(md) {
        display: flex !important;
        flex-direction: column !important;
        gap: var(--spacing-sm) !important;
        
        .el-button {
          width: 100% !important;
          margin: 0 !important;
        }
      }
    }
  }
}

/* ===== 弹窗响应式Mixin ===== */
@mixin responsive-dialog {
  @include respond-to(md) {
    width: 95% !important;
    margin: 2vh auto !important;
    max-height: 96vh !important;
    
    .el-dialog__header {
      padding: var(--spacing-md) !important;
    }
    
    .el-dialog__body {
      padding: var(--spacing-md) !important;
      max-height: 70vh !important;
      overflow-y: auto !important;
    }
    
    .el-dialog__footer {
      padding: var(--spacing-md) !important;
      
      .el-button {
        width: 100% !important;
        margin: 0 0 var(--spacing-sm) 0 !important;
        
        &:last-child {
          margin-bottom: 0 !important;
        }
      }
    }
  }
  
  @include respond-to(sm) {
    width: 100% !important;
    margin: 0 !important;
    height: 100vh !important;
    max-height: 100vh !important;
    border-radius: 0 !important;
  }
}

/* ===== 卡片响应式Mixin ===== */
@mixin responsive-card {
  .el-card__header {
    @include respond-to(sm) {
      padding: 12px !important;
      
      .clearfix {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 8px !important;
        
        .el-button {
          width: 100% !important;
        }
      }
    }
  }
  
  .el-card__body {
    @include respond-to(sm) {
      padding: 12px !important;
    }
  }
}

/* ===== 侧边栏响应式Mixin ===== */
@mixin responsive-sidebar {
  .el-aside {
    .aside-box {
      transition: width 0.3s ease;
      
      @include respond-to(lg) {
        width: var(--sidebar-width-mobile) !important;
      }
      
      @include respond-to(md) {
        width: var(--sidebar-width-collapsed) !important;
        
        .logo-text {
          display: none !important;
        }
        
        .el-menu-item__title,
        .el-sub-menu__title {
          display: none !important;
        }
      }
    }
  }
}

/* ===== 工具类生成Mixin ===== */
@mixin generate-responsive-utilities {
  @each $breakpoint in (xs, sm, md, lg, xl) {
    .hidden-#{$breakpoint} {
      @include respond-to($breakpoint) {
        display: none !important;
      }
    }
    
    .visible-#{$breakpoint} {
      display: none !important;
      @include respond-to($breakpoint) {
        display: block !important;
      }
    }
  }
}

/* 生成响应式工具类 */
@include generate-responsive-utilities;
