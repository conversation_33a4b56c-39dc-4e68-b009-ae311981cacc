/* 响应式样式 - 全面优化版本 */

/* ===== CSS 变量定义 ===== */
:root {
  /* 断点变量 */
  --breakpoint-xs: 480px;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-xxl: 1440px;

  /* 响应式间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* 响应式字体大小 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;

  /* 响应式容器宽度 */
  --container-padding-mobile: 10px;
  --container-padding-tablet: 20px;
  --container-padding-desktop: 24px;

  /* 侧边栏宽度 */
  --sidebar-width-expanded: 210px;
  --sidebar-width-collapsed: 64px;
  --sidebar-width-mobile: 180px;
}

/* ===== 响应式Mixin类 ===== */
.responsive-container {
  padding: var(--container-padding-mobile);

  @media screen and (width >= 768px) {
    padding: var(--container-padding-tablet);
  }

  @media screen and (width >= 1200px) {
    padding: var(--container-padding-desktop);
  }
}
.responsive-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-md);

  @media screen and (width >= 768px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media screen and (width >= 1200px) {
    grid-template-columns: repeat(3, 1fr);
  }
}
.responsive-flex {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);

  @media screen and (width >= 768px) {
    flex-direction: row;
  }
}

/* ===== 超大屏幕 (>=1440px) ===== */
@media screen and (width >= 1440px) {
  .el-container {
    .el-aside {
      .aside-box {
        width: 240px !important;
      }
    }
    .el-header {
      padding: 0 32px !important;
    }
    .el-main {
      padding: 32px !important;
    }
  }

  /* 增大字体和间距 */
  .main-box {
    gap: 32px !important;
    padding: 32px !important;
  }
}

/* ===== 大屏幕 (>=1200px && <1440px) ===== */
@media screen and (width >= 1200px) and (width <= 1439px) {
  .el-container {
    .el-aside {
      .aside-box {
        width: var(--sidebar-width-expanded) !important;
      }
    }
  }
  .main-box {
    gap: 24px !important;
    padding: 24px !important;
  }
}

/* ===== 中等屏幕 (>=992px && <1200px) ===== */
@media screen and (width >= 992px) and (width <= 1199px) {
  .el-container {
    .el-aside {
      .aside-box {
        width: 200px !important;
      }
    }
    .el-header {
      padding: 0 20px !important;
    }
    .el-main {
      padding: 20px !important;
    }
  }
  .main-box {
    gap: 20px !important;
    padding: 20px !important;
  }

  /* 调整卡片布局 */
  .el-row {
    .el-col {
      &[class*="span-24"] {
        flex: 0 0 100% !important;
        max-width: 100% !important;
      }
    }
  }
}

/* ===== 平板 (>=768px && <992px) ===== */
@media screen and (width >= 768px) and (width <= 991px) {
  .el-container {
    .el-aside {
      .aside-box {
        width: var(--sidebar-width-mobile) !important;
        .logo {
          .logo-text {
            font-size: 18px !important;
          }
        }
      }
    }
    .el-header {
      height: 50px !important;
      padding: 0 16px !important;
      .header-lf,
      .header-ri {
        .header-icon {
          margin-right: 8px !important;
        }
      }
    }
    .el-main {
      padding: 16px !important;
    }
  }
  .main-box {
    gap: 16px !important;
    padding: 16px !important;
  }

  /* 表格在平板上的优化 */
  .el-table {
    font-size: 14px !important;
    .el-table__header th {
      padding: 8px 0 !important;
    }
    .el-table__row td {
      padding: 8px 0 !important;
    }
  }

  /* 表单在平板上的优化 */
  .el-form {
    .el-form-item {
      margin-bottom: 16px !important;
      .el-form-item__label {
        font-size: 14px !important;
      }
    }
  }
}

/* ===== 移动设备 (<768px) ===== */
@media screen and (width <= 767px) {
  /* 全局移动端优化 */
  #app {
    min-width: auto !important;
  }
  body {
    font-size: var(--font-size-sm) !important;
  }

  /* 布局容器优化 */
  .el-container {
    .el-aside {
      .aside-box {
        width: var(--sidebar-width-collapsed) !important;
        .logo {
          .logo-text {
            display: none !important;
          }
          .logo-img {
            width: 24px !important;
            height: 24px !important;
          }
        }
        .el-menu {
          .el-menu-item,
          .el-sub-menu__title {
            padding: 0 16px !important;
            .el-menu-item__title,
            .el-sub-menu__title {
              display: none !important;
            }
          }
        }
      }
    }
    .el-header {
      height: 48px !important;
      padding: 0 var(--container-padding-mobile) !important;
      .header-lf {
        .tool-bar-ri {
          display: none !important;
        }
        .header-icon {
          margin-right: 8px !important;
        }
      }
      .header-ri {
        .el-breadcrumb {
          display: none !important;
        }
        .header-icon {
          margin-right: 6px !important;
          &:last-child {
            margin-right: 0 !important;
          }
        }
      }
    }
    .el-main {
      padding: var(--container-padding-mobile) !important;
    }
  }

  /* 标签页优化 */
  .tags-view {
    padding: 0 var(--container-padding-mobile) !important;
    .tags-view-item {
      padding: 4px 8px !important;
      margin-right: 4px !important;
      font-size: var(--font-size-xs) !important;
    }
  }

  /* 主要内容区域 */
  .main-box {
    flex-direction: column !important;
    gap: var(--spacing-md) !important;
    padding: var(--container-padding-mobile) !important;
    .tree-filter {
      order: 2 !important;
      width: 100% !important;
      min-width: auto !important;
      margin-right: 0 !important;
      margin-bottom: var(--spacing-md) !important;
    }
    .table-box {
      order: 1 !important;
    }
  }

  /* 表格移动端优化 */
  .el-table {
    width: 100% !important;
    overflow-x: auto !important;
    font-size: var(--font-size-xs) !important;
    .el-table__header {
      th {
        padding: 6px 4px !important;
        font-size: var(--font-size-xs) !important;
        white-space: nowrap !important;
      }
    }
    .el-table__body {
      td {
        padding: 6px 4px !important;
        font-size: var(--font-size-xs) !important;
      }
    }

    /* 隐藏不重要的列 */
    .el-table__column--selection,
    .el-table__expand-column {
      display: none !important;
    }
  }

  /* 表单移动端优化 */
  .el-form {
    .el-form-item {
      margin-bottom: var(--spacing-md) !important;
      .el-form-item__label {
        display: block !important;
        margin-bottom: var(--spacing-xs) !important;
        font-size: var(--font-size-sm) !important;
        line-height: 1.4 !important;
        text-align: left !important;
      }
      .el-form-item__content {
        margin-left: 0 !important;
        .el-input,
        .el-select,
        .el-textarea {
          width: 100% !important;
        }
        .el-input__wrapper {
          font-size: var(--font-size-sm) !important;
        }
      }
    }

    /* 表单按钮组 */
    .el-form-item__content {
      .el-button-group,
      .form-buttons {
        display: flex !important;
        flex-direction: column !important;
        gap: var(--spacing-sm) !important;
        .el-button {
          width: 100% !important;
          margin: 0 !important;
        }
      }
    }
  }

  /* 弹窗移动端优化 */
  .el-dialog {
    width: 95% !important;
    max-height: 96vh !important;
    margin: 2vh auto !important;
    .el-dialog__header {
      padding: var(--spacing-md) !important;
      .el-dialog__title {
        font-size: var(--font-size-md) !important;
      }
    }
    .el-dialog__body {
      max-height: 70vh !important;
      padding: var(--spacing-md) !important;
      overflow-y: auto !important;
    }
    .el-dialog__footer {
      padding: var(--spacing-md) !important;
      .el-button {
        width: 100% !important;
        margin: 0 0 var(--spacing-sm) 0 !important;
        &:last-child {
          margin-bottom: 0 !important;
        }
      }
    }
  }

  /* 抽屉移动端优化 */
  .el-drawer {
    .el-drawer__header {
      padding: var(--spacing-md) !important;
    }
    .el-drawer__body {
      padding: var(--spacing-md) !important;
    }
  }

  /* 下拉菜单优化 */
  .el-dropdown-menu {
    min-width: 140px !important;
    .el-dropdown-menu__item {
      padding: 8px 12px !important;
      font-size: var(--font-size-sm) !important;
    }
  }

  /* 分页组件优化 */
  .el-pagination {
    flex-wrap: wrap !important;
    justify-content: center !important;
    .el-pagination__total,
    .el-pagination__jump {
      display: none !important;
    }
    .el-pager {
      .number {
        min-width: 28px !important;
        height: 28px !important;
        font-size: var(--font-size-xs) !important;
        line-height: 28px !important;
      }
    }
  }
}

/* ===== 小型移动设备 (<576px) ===== */
@media screen and (width <= 575px) {
  /* 进一步优化小屏幕体验 */
  .el-container {
    .el-header {
      height: 44px !important;
      padding: 0 8px !important;
      .header-lf {
        .tool-bar-ri {
          display: none !important;
        }
        .collapse-icon {
          margin-right: 4px !important;
        }
      }
      .header-ri {
        .el-dropdown {
          margin-left: 4px !important;
        }

        /* 只保留最重要的图标 */
        .header-icon:not(.el-dropdown, .user-avatar) {
          display: none !important;
        }

        /* 用户头像保持显示但缩小 */
        .user-avatar {
          .el-avatar {
            width: 28px !important;
            height: 28px !important;
          }
        }
      }
    }
  }

  /* 主内容区域进一步压缩 */
  .main-box {
    gap: 8px !important;
    padding: 8px !important;
  }

  /* 卡片组件优化 */
  .el-card {
    .el-card__header {
      padding: 12px !important;
      .clearfix {
        flex-direction: column !important;
        gap: 8px !important;
        align-items: flex-start !important;
        span {
          font-size: var(--font-size-sm) !important;
        }
        .el-button {
          width: 100% !important;
          font-size: var(--font-size-xs) !important;
        }
      }
    }
    .el-card__body {
      padding: 12px !important;
    }
  }

  /* 表单进一步优化 */
  .el-form {
    .el-form-item {
      margin-bottom: 12px !important;
      .el-form-item__label {
        margin-bottom: 4px !important;
        font-size: var(--font-size-xs) !important;
      }
      .el-form-item__content {
        .el-input__wrapper {
          padding: 6px 8px !important;
          font-size: var(--font-size-xs) !important;
        }
        .el-button {
          padding: 6px 12px !important;
          font-size: var(--font-size-xs) !important;
        }
      }
    }
  }

  /* 表格进一步压缩 */
  .el-table {
    .el-table__header th,
    .el-table__body td {
      padding: 4px 2px !important;
      font-size: 11px !important;
    }

    /* 在极小屏幕上隐藏更多列 */
    .el-table__column:nth-child(n + 4) {
      display: none !important;
    }
  }

  /* 弹窗全屏化 */
  .el-dialog {
    width: 100% !important;
    height: 100vh !important;
    max-height: 100vh !important;
    margin: 0 !important;
    border-radius: 0 !important;
    .el-dialog__header {
      padding: 12px !important;
      border-bottom: 1px solid var(--el-border-color-lighter) !important;
    }
    .el-dialog__body {
      height: calc(100vh - 120px) !important;
      padding: 12px !important;
      overflow-y: auto !important;
    }
    .el-dialog__footer {
      padding: 12px !important;
      border-top: 1px solid var(--el-border-color-lighter) !important;
    }
  }

  /* 标签页优化 */
  .el-tabs {
    .el-tabs__header {
      margin-bottom: 8px !important;
    }
    .el-tabs__item {
      height: 32px !important;
      padding: 0 8px !important;
      font-size: var(--font-size-xs) !important;
      line-height: 32px !important;
    }
  }

  /* 按钮组优化 */
  .el-button-group {
    .el-button {
      padding: 4px 8px !important;
      font-size: var(--font-size-xs) !important;
    }
  }

  /* 消息提示优化 */
  .el-message {
    min-width: 280px !important;
    max-width: 90% !important;
  }

  /* 工具提示优化 */
  .el-tooltip__popper {
    max-width: 200px !important;
    font-size: var(--font-size-xs) !important;
  }
}

/* ===== 超小型设备 (<480px) ===== */
@media screen and (width <= 479px) {
  /* 极限压缩优化 */
  .el-container {
    .el-header {
      height: 40px !important;
      padding: 0 4px !important;
      .header-lf {
        .collapse-icon {
          margin-right: 2px !important;
        }
      }
      .header-ri {
        .user-avatar .el-avatar {
          width: 24px !important;
          height: 24px !important;
        }
      }
    }
  }
  .main-box {
    gap: 4px !important;
    padding: 4px !important;
  }

  /* 表格在极小屏幕上的处理 */
  .el-table {
    /* 只显示最重要的前两列 */
    .el-table__column:nth-child(n + 3) {
      display: none !important;
    }
  }

  /* 弹窗按钮垂直排列 */
  .el-dialog__footer {
    .el-button {
      display: block !important;
      width: 100% !important;
      margin: 0 0 8px !important;
    }
  }
}

/* ===== 响应式工具类 ===== */
.hidden-xs {
  @media screen and (width <= 575px) {
    display: none !important;
  }
}
.hidden-sm {
  @media screen and (width <= 767px) {
    display: none !important;
  }
}
.hidden-md {
  @media screen and (width <= 991px) {
    display: none !important;
  }
}
.hidden-lg {
  @media screen and (width <= 1199px) {
    display: none !important;
  }
}
.visible-xs {
  display: none !important;

  @media screen and (width <= 575px) {
    display: block !important;
  }
}
.visible-sm {
  display: none !important;

  @media screen and (width <= 767px) {
    display: block !important;
  }
}

/* 响应式文字大小 */
.text-responsive {
  font-size: var(--font-size-lg);

  @media screen and (width <= 991px) {
    font-size: var(--font-size-md);
  }

  @media screen and (width <= 767px) {
    font-size: var(--font-size-sm);
  }

  @media screen and (width <= 575px) {
    font-size: var(--font-size-xs);
  }
}

/* 响应式间距 */
.spacing-responsive {
  padding: var(--spacing-xl);

  @media screen and (width <= 991px) {
    padding: var(--spacing-lg);
  }

  @media screen and (width <= 767px) {
    padding: var(--spacing-md);
  }

  @media screen and (width <= 575px) {
    padding: var(--spacing-sm);
  }
}
