/* 异地组网页面响应式样式 */
@import "./mixins/responsive.scss";

/* ===== 异地组网页面布局优化 ===== */

/* 移动端垂直布局 - 只在小屏幕应用 */
@media screen and (width <= 575px) {
  .main-box.rnet-page {
    flex-direction: column !important;
    gap: var(--spacing-md) !important;
    padding: var(--container-padding-mobile) !important;

    .tree-filter {
      width: 100% !important;
      min-width: auto !important;
      order: 2 !important;
      margin-right: 0 !important;
      margin-bottom: var(--spacing-md) !important;
    }

    .main-content {
      order: 1 !important;
    }
  }
}

.rnet-page {

  /* 大屏幕优化 */
  @include respond-above(xl) {
    &.main-box {
      gap: 24px !important;
      padding: 24px !important;

      .main-content {
        padding: 24px;
      }
    }

    .box-card {
      .clearfix {
        .action-button {
          padding: 10px 20px;
        }
      }
    }
  }

  /* 中等屏幕优化 */
  @include respond-between(768px, 1199px) {
    &.main-box {
      gap: 16px !important;
      padding: 16px !important;

      .main-content {
        padding: 16px;
      }
    }

    .box-card {
      .clearfix {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        .action-button {
          width: 100%;
          justify-content: center;
        }
      }
    }
  }

  /* 移动设备优化 - 只在真正的小屏幕上应用 */
  @include respond-to(sm) {
    &.main-box {
      flex-direction: column !important;
      gap: var(--spacing-md) !important;
      padding: var(--container-padding-mobile) !important;
    }

    .tree-filter {
      width: 100% !important;
      margin-right: 0 !important;
      margin-bottom: var(--spacing-md) !important;
      order: 2 !important;
    }

    .main-content {
      order: 1 !important;
      padding: 12px !important;
      
      .box-card {
        .el-card__header {
          padding: 12px !important;
          
          .clearfix {
            flex-direction: column !important;
            align-items: center !important;
            gap: 8px !important;
            
            span {
              font-size: var(--font-size-md) !important;
              text-align: center !important;
            }
            
            .action-button {
              width: 100% !important;
              font-size: var(--font-size-sm) !important;
            }
          }
        }
        
        .el-card__body {
          padding: 12px !important;
        }
      }
    }
    
    /* 网络图容器优化 */
    .network-container {
      width: 100% !important;
      height: 300px !important;
      overflow: hidden !important;
      
      .d3-network-graph {
        width: 100% !important;
        height: 100% !important;
      }
    }
    
    /* 设备列表优化 */
    .device-list {
      .device-item {
        padding: 8px !important;
        margin-bottom: 8px !important;
        
        .device-info {
          font-size: var(--font-size-sm) !important;
        }
        
        .device-actions {
          flex-direction: column !important;
          gap: 4px !important;
          
          .el-button {
            width: 100% !important;
            font-size: var(--font-size-xs) !important;
          }
        }
      }
    }
    
    /* 添加设备对话框优化 */
    .add-device-dialog {
      .el-dialog {
        width: 95% !important;
        margin: 2vh auto !important;
        
        .el-dialog__body {
          padding: 12px !important;
          
          .device-selection {
            .el-checkbox-group {
              display: flex !important;
              flex-direction: column !important;
              gap: 8px !important;
              
              .el-checkbox {
                margin-right: 0 !important;
                
                .el-checkbox__label {
                  font-size: var(--font-size-sm) !important;
                }
              }
            }
          }
        }
      }
    }
  }
  
  /* 小屏幕进一步优化 */
  @include respond-to(sm) {
    .main-content {
      padding: 8px !important;
      
      .box-card {
        .el-card__header {
          padding: 8px !important;
          
          .clearfix {
            span {
              font-size: var(--font-size-sm) !important;
            }
            
            .action-button {
              font-size: var(--font-size-xs) !important;
              padding: 6px 12px !important;
            }
          }
        }
        
        .el-card__body {
          padding: 8px !important;
        }
      }
    }
    
    /* 网络图进一步压缩 */
    .network-container {
      height: 250px !important;
    }
    
    /* 设备列表进一步优化 */
    .device-list {
      .device-item {
        padding: 6px !important;
        
        .device-info {
          font-size: var(--font-size-xs) !important;
        }
        
        .device-actions {
          .el-button {
            font-size: 11px !important;
            padding: 4px 8px !important;
          }
        }
      }
    }
  }
}

/* ===== D3网络图响应式优化 ===== */

.d3-network-graph {
  /* 移动设备优化 */
  @include respond-to(md) {
    .node-label {
      font-size: 10px !important;
    }
    
    .link-label {
      font-size: 9px !important;
    }
    
    .node-circle {
      r: 8 !important;
    }
    
    .legend {
      font-size: var(--font-size-xs) !important;
      
      .legend-item {
        margin-bottom: 4px !important;
      }
    }
  }
  
  @include respond-to(sm) {
    .node-label {
      display: none !important; /* 在小屏幕上隐藏节点标签 */
    }
    
    .node-circle {
      r: 6 !important;
    }
    
    .legend {
      display: none !important; /* 在小屏幕上隐藏图例 */
    }
  }
}

/* ===== 异地组网表单响应式优化 ===== */

.rnet-form {
  @include respond-to(md) {
    .el-form-item {
      margin-bottom: var(--spacing-md) !important;
      
      .el-form-item__label {
        display: block !important;
        text-align: left !important;
        margin-bottom: var(--spacing-xs) !important;
        font-size: var(--font-size-sm) !important;
        width: 100% !important;
      }
      
      .el-form-item__content {
        margin-left: 0 !important;
        
        .el-input,
        .el-select {
          width: 100% !important;
        }
        
        .el-input-group {
          .el-input-group__prepend,
          .el-input-group__append {
            padding: 0 8px !important;
            font-size: var(--font-size-sm) !important;
          }
        }
      }
    }
    
    /* IP地址输入组优化 */
    .ip-input-group {
      display: flex !important;
      flex-direction: column !important;
      gap: var(--spacing-sm) !important;
      
      .el-input {
        width: 100% !important;
      }
    }
    
    /* 按钮组优化 */
    .form-buttons {
      display: flex !important;
      flex-direction: column !important;
      gap: var(--spacing-sm) !important;
      
      .el-button {
        width: 100% !important;
        margin: 0 !important;
      }
    }
  }
  
  @include respond-to(sm) {
    .el-form-item {
      margin-bottom: 12px !important;
      
      .el-form-item__label {
        font-size: var(--font-size-xs) !important;
      }
      
      .el-form-item__content {
        .el-input__wrapper {
          font-size: var(--font-size-xs) !important;
        }
        
        .el-button {
          font-size: var(--font-size-xs) !important;
          padding: 6px 12px !important;
        }
      }
    }
  }
}

/* ===== 异地组网设备配置对话框优化 ===== */

.rnet-config-dialog {
  @include respond-to(md) {
    .el-dialog {
      width: 95% !important;
      margin: 2vh auto !important;
      
      .el-dialog__header {
        padding: 12px !important;
        
        .el-dialog__title {
          font-size: var(--font-size-md) !important;
        }
      }
      
      .el-dialog__body {
        padding: 12px !important;
        max-height: 70vh !important;
        overflow-y: auto !important;
        
        .config-form {
          .el-tabs {
            .el-tabs__header {
              .el-tabs__item {
                padding: 0 12px !important;
                font-size: var(--font-size-sm) !important;
              }
            }
            
            .el-tabs__content {
              padding: var(--spacing-md) 0 !important;
            }
          }
        }
      }
      
      .el-dialog__footer {
        padding: 12px !important;
        
        .el-button {
          width: 100% !important;
          margin: 0 0 var(--spacing-sm) 0 !important;
          
          &:last-child {
            margin-bottom: 0 !important;
          }
        }
      }
    }
  }
  
  @include respond-to(sm) {
    .el-dialog {
      width: 100% !important;
      height: 100vh !important;
      margin: 0 !important;
      border-radius: 0 !important;
      
      .el-dialog__body {
        height: calc(100vh - 120px) !important;
      }
    }
  }
}

/* ===== 异地组网状态指示器优化 ===== */

.rnet-status {
  @include respond-to(md) {
    .status-indicator {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      
      .status-dot {
        width: 8px !important;
        height: 8px !important;
        margin-right: 4px !important;
      }
      
      .status-text {
        font-size: var(--font-size-xs) !important;
      }
    }
  }
  
  @include respond-to(sm) {
    .status-indicator {
      .status-text {
        display: none !important; /* 只显示状态点 */
      }
    }
  }
}
