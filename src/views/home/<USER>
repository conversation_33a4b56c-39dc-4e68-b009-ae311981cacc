/* 暗黑模式样式 */
html.dark {
  .main-box {
    &::before {
      background: radial-gradient(circle, rgba(70, 130, 180, 0.3) 0%, rgba(70, 130, 180, 0) 70%);
    }

    &::after {
      background: radial-gradient(circle, rgba(255, 100, 100, 0.3) 0%, rgba(255, 100, 100, 0) 70%);
    }

    .main-content {
      .box-card {
        background: rgba(30, 30, 30, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.1);

        &:hover {
          background: rgba(40, 40, 40, 0.4);
          box-shadow: 0 12px 32px rgba(0, 0, 0, 0.3);
        }
      }

      .chart-container {
        &::before {
          background: linear-gradient(135deg, rgba(40, 40, 40, 0.1) 0%, rgba(30, 30, 30, 0.2) 100%);
        }
      }

      .message-container {
        &::before {
          background: linear-gradient(135deg, rgba(40, 40, 40, 0.1) 0%, rgba(30, 30, 30, 0.2) 100%);
        }

        &::-webkit-scrollbar-thumb {
          background-color: rgba(255, 255, 255, 0.1);

          &:hover {
            background-color: var(--el-color-primary-dark-2);
          }
        }

        &::-webkit-scrollbar-track {
          background-color: rgba(0, 0, 0, 0.2);
        }
      }

      .message-card {
        background: rgba(30, 30, 30, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.1);

        &:hover {
          background: rgba(40, 40, 40, 0.4);
          box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
          border-color: var(--el-color-primary-dark-2);
        }

        .message-header {
          h4 {
            color: var(--el-text-color-primary);
          }

          .message-time {
            color: var(--el-text-color-secondary);
          }
        }

        .message-content p {
          color: var(--el-text-color-regular);
        }
      }
    }
  }
}

@import "@/styles/mixins/responsive.scss";

.main-box {
  display: flex;
  height: 100%;
  background: var(--el-bg-color-page);
  position: relative;
  overflow: hidden;

  /* 响应式布局调整 */
  @include respond-to(md) {
    flex-direction: column;
    height: auto;
    min-height: 100%;
  }

  /* 装饰元素 - 左下角光晕 */
  &::before {
    content: "";
    position: absolute;
    bottom: -150px;
    left: -150px;
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(173, 216, 230, 0.5) 0%, rgba(173, 216, 230, 0) 70%);
    opacity: 0.6;
    z-index: 0;
  }

  /* 装饰元素 - 右上角光晕 */
  &::after {
    content: "";
    position: absolute;
    top: -100px;
    right: -100px;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(255, 182, 193, 0.5) 0%, rgba(255, 182, 193, 0) 70%);
    opacity: 0.4;
    z-index: 0;
  }

  .main-content {
    flex: 1;
    padding: 24px;
    overflow: auto;
    position: relative;
    z-index: 1;

    @include respond-to(lg) {
      padding: 20px;
    }

    @include respond-to(md) {
      padding: 16px;
    }

    @include respond-to(sm) {
      padding: 12px;
    }

    /* 响应式网格布局 */
    .el-row {
      @include respond-to(md) {
        .el-col {
          &:not(:last-child) {
            margin-bottom: var(--spacing-md);
          }
        }
      }
    }

    .box-card {
      @include responsive-card;
      display: flex;
      flex-direction: column;
      /* 使用毛玻璃效果类的样式，但不使用!important以允许暗黑模式覆盖 */
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.18);
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      @include respond-to(md) {
        border-radius: 12px;
        box-shadow: 0 6px 24px rgba(0, 0, 0, 0.1);
      }

      @include respond-to(sm) {
        border-radius: 8px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      }

      &:hover {
        box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
        transform: translateY(-5px);
        background: rgba(255, 255, 255, 0.2);

        @include respond-to(md) {
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
          transform: translateY(-3px);
        }

        @include respond-to(sm) {
          box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
          transform: translateY(-2px);
        }
      }
      .el-card__header {
        padding: 20px 24px;
        border-bottom: 1px solid var(--el-border-color-lighter);
        .clearfix {
          display: flex;
          align-items: center;
          justify-content: space-between;
          span {
            position: relative;
            padding-left: 12px;
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            &::before {
              position: absolute;
              top: 50%;
              left: 0;
              width: 4px;
              height: 16px;
              content: "";
              background: var(--el-color-primary);
              border-radius: 2px;
              transform: translateY(-50%);
            }
          }
          .el-button {
            padding: 8px 16px;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.3s ease;
            &:hover {
              box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
              transform: translateY(-1px);
            }

            &.action-button {
              position: relative;
              overflow: hidden;
              padding: 10px 20px;
              font-size: 15px;
              font-weight: 600;
              letter-spacing: 0.5px;
              border-radius: 10px;
              box-shadow: 0 6px 16px rgba(var(--el-color-primary-rgb), 0.3);

              &::before {
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                content: "";
                background: linear-gradient(
                  90deg,
                  rgba(255, 255, 255, 0) 0%,
                  rgba(255, 255, 255, 0.2) 50%,
                  rgba(255, 255, 255, 0) 100%
                );
                transition: all 0.8s ease;
              }

              &:hover {
                box-shadow: 0 8px 20px rgba(var(--el-color-primary-rgb), 0.5);
                transform: translateY(-3px);

                &::before {
                  left: 100%;
                }
              }

              .el-icon {
                transition: all 0.3s ease;
                font-size: 16px;
                margin-right: 6px;
              }

              &:hover .el-icon {
                transform: scale(1.2);
              }

              // 暗色模式适配
              .dark & {
                box-shadow: 0 6px 16px rgba(var(--el-color-primary-rgb), 0.2);

                &::before {
                  background: linear-gradient(90deg, rgba(0, 0, 0, 0) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(0, 0, 0, 0) 100%);
                }

                &:hover {
                  box-shadow: 0 8px 20px rgba(var(--el-color-primary-rgb), 0.3);
                }
              }
            }

            &.refresh-button {
              margin-right: 10px;
            }
          }
        }
      }
      .el-card__body {
        padding: 24px;
        box-sizing: border-box;
        display: flex;
      }
    }
  }
}
/* 确保两个卡片高度一致的共享样式 */
.chart-container,
.message-container {
  width: 100%;
  height: 400px;
  position: relative;
  box-sizing: border-box;
  margin: 0;
  flex: 1;

  /* 添加微妙的背景效果 */
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);
    border-radius: 8px;
    z-index: -1;
  }
}

/* 消息容器特有样式 */
.message-container {
  overflow-y: auto;
  padding: 0;

  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color-darker);
    border-radius: 3px;

    &:hover {
      background-color: var(--el-color-primary-light-5);
    }
  }

  &::-webkit-scrollbar-track {
    background-color: var(--el-border-color-lighter);
    border-radius: 3px;
  }
}
.message-card {
  margin-bottom: 16px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  border-radius: 12px;
  transition: all 0.3s ease;

  &:hover {
    border-color: var(--el-color-primary-light-7);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.2);
  }
  .message-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    h4 {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
    .message-time {
      font-size: 13px;
      color: var(--el-text-color-secondary);
    }
  }
  .message-content {
    p {
      margin: 0;
      font-size: 14px;
      line-height: 1.6;
      color: var(--el-text-color-regular);
    }
  }
}
:deep(.el-timeline-item__node) {
  background-color: var(--el-color-primary-light-7);
  border-color: var(--el-color-primary);
}
:deep(.el-timeline-item__tail) {
  border-left-color: var(--el-border-color-lighter);
}
:deep(.el-timeline-item__timestamp) {
  font-size: 13px;
  color: var(--el-text-color-secondary);
}
