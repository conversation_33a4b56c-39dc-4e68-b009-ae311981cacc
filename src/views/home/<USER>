/* 暗黑模式样式 */
html.dark {
  .main-box {
    &::before {
      background: radial-gradient(circle, rgb(70 130 180 / 30%) 0%, rgb(70 130 180 / 0%) 70%);
    }
    &::after {
      background: radial-gradient(circle, rgb(255 100 100 / 30%) 0%, rgb(255 100 100 / 0%) 70%);
    }
    .main-content {
      .box-card {
        background: rgb(30 30 30 / 30%);
        border: 1px solid rgb(255 255 255 / 10%);
        &:hover {
          background: rgb(40 40 40 / 40%);
          box-shadow: 0 12px 32px rgb(0 0 0 / 30%);
        }
      }
      .chart-container {
        &::before {
          background: linear-gradient(135deg, rgb(40 40 40 / 10%) 0%, rgb(30 30 30 / 20%) 100%);
        }
      }
      .message-container {
        &::before {
          background: linear-gradient(135deg, rgb(40 40 40 / 10%) 0%, rgb(30 30 30 / 20%) 100%);
        }
        &::-webkit-scrollbar-thumb {
          background-color: rgb(255 255 255 / 10%);
          &:hover {
            background-color: var(--el-color-primary-dark-2);
          }
        }
        &::-webkit-scrollbar-track {
          background-color: rgb(0 0 0 / 20%);
        }
      }
      .message-card {
        background: rgb(30 30 30 / 30%);
        border: 1px solid rgb(255 255 255 / 10%);
        &:hover {
          background: rgb(40 40 40 / 40%);
          border-color: var(--el-color-primary-dark-2);
          box-shadow: 0 8px 20px rgb(0 0 0 / 30%);
        }
        .message-header {
          h4 {
            color: var(--el-text-color-primary);
          }
          .message-time {
            color: var(--el-text-color-secondary);
          }
        }
        .message-content p {
          color: var(--el-text-color-regular);
        }
      }
    }
  }
}

@import "@/styles/mixins/responsive";
.main-box {
  position: relative;
  display: flex;
  height: 100%;
  overflow: hidden;
  background: var(--el-bg-color-page);

  /* 响应式布局调整 */
  @include respond-to(md) {
    flex-direction: column;
    height: auto;
    min-height: 100%;
  }

  /* 装饰元素 - 左下角光晕 */
  &::before {
    position: absolute;
    bottom: -150px;
    left: -150px;
    z-index: 0;
    width: 400px;
    height: 400px;
    content: "";
    background: radial-gradient(circle, rgb(173 216 230 / 50%) 0%, rgb(173 216 230 / 0%) 70%);
    opacity: 0.6;
  }

  /* 装饰元素 - 右上角光晕 */
  &::after {
    position: absolute;
    top: -100px;
    right: -100px;
    z-index: 0;
    width: 300px;
    height: 300px;
    content: "";
    background: radial-gradient(circle, rgb(255 182 193 / 50%) 0%, rgb(255 182 193 / 0%) 70%);
    opacity: 0.4;
  }
  .main-content {
    position: relative;
    z-index: 1;
    flex: 1;
    padding: 24px;
    overflow: auto;

    @include respond-to(lg) {
      padding: 20px;
    }

    @include respond-to(md) {
      padding: 16px;
    }

    @include respond-to(sm) {
      padding: 12px;
    }

    /* 响应式网格布局 */
    .el-row {
      @include respond-to(md) {
        .el-col {
          &:not(:last-child) {
            margin-bottom: var(--spacing-md);
          }
        }
      }
    }
    .box-card {
      @include responsive-card;

      display: flex;
      flex-direction: column;

      /* 使用毛玻璃效果类的样式，但不使用!important以允许暗黑模式覆盖 */
      background: rgb(255 255 255 / 10%);
      backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
      border: 1px solid rgb(255 255 255 / 18%);
      border-radius: 16px;
      box-shadow: 0 8px 32px rgb(0 0 0 / 10%);
      transition: all 0.3s ease;

      @include respond-to(md) {
        border-radius: 12px;
        box-shadow: 0 6px 24px rgb(0 0 0 / 10%);
      }

      @include respond-to(sm) {
        border-radius: 8px;
        box-shadow: 0 4px 16px rgb(0 0 0 / 10%);
      }
      &:hover {
        background: rgb(255 255 255 / 20%);
        box-shadow: 0 12px 32px rgb(0 0 0 / 15%);
        transform: translateY(-5px);

        @include respond-to(md) {
          box-shadow: 0 8px 24px rgb(0 0 0 / 12%);
          transform: translateY(-3px);
        }

        @include respond-to(sm) {
          box-shadow: 0 6px 16px rgb(0 0 0 / 10%);
          transform: translateY(-2px);
        }
      }
      .el-card__header {
        padding: 20px 24px;
        border-bottom: 1px solid var(--el-border-color-lighter);
        .clearfix {
          display: flex;
          align-items: center;
          justify-content: space-between;
          span {
            position: relative;
            padding-left: 12px;
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            &::before {
              position: absolute;
              top: 50%;
              left: 0;
              width: 4px;
              height: 16px;
              content: "";
              background: var(--el-color-primary);
              border-radius: 2px;
              transform: translateY(-50%);
            }
          }
          .el-button {
            padding: 8px 16px;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.3s ease;
            &:hover {
              box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
              transform: translateY(-1px);
            }
            &.action-button {
              position: relative;
              padding: 10px 20px;
              overflow: hidden;
              font-size: 15px;
              font-weight: 600;
              letter-spacing: 0.5px;
              border-radius: 10px;
              box-shadow: 0 6px 16px rgba(var(--el-color-primary-rgb), 0.3);
              &::before {
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                content: "";
                background: linear-gradient(
                  90deg,
                  rgb(255 255 255 / 0%) 0%,
                  rgb(255 255 255 / 20%) 50%,
                  rgb(255 255 255 / 0%) 100%
                );
                transition: all 0.8s ease;
              }
              &:hover {
                box-shadow: 0 8px 20px rgba(var(--el-color-primary-rgb), 0.5);
                transform: translateY(-3px);
                &::before {
                  left: 100%;
                }
              }
              .el-icon {
                margin-right: 6px;
                font-size: 16px;
                transition: all 0.3s ease;
              }
              &:hover .el-icon {
                transform: scale(1.2);
              }

              // 暗色模式适配
              .dark & {
                box-shadow: 0 6px 16px rgba(var(--el-color-primary-rgb), 0.2);
                &::before {
                  background: linear-gradient(90deg, rgb(0 0 0 / 0%) 0%, rgb(255 255 255 / 10%) 50%, rgb(0 0 0 / 0%) 100%);
                }
                &:hover {
                  box-shadow: 0 8px 20px rgba(var(--el-color-primary-rgb), 0.3);
                }
              }
            }
            &.refresh-button {
              margin-right: 10px;
            }
          }
        }
      }
      .el-card__body {
        box-sizing: border-box;
        display: flex;
        padding: 24px;
      }
    }
  }
}

/* 确保两个卡片高度一致的共享样式 */
.chart-container,
.message-container {
  position: relative;
  box-sizing: border-box;
  flex: 1;
  width: 100%;
  height: 400px;
  margin: 0;

  /* 添加微妙的背景效果 */
  &::before {
    position: absolute;
    inset: 0;
    z-index: -1;
    content: "";
    background: linear-gradient(135deg, rgb(255 255 255 / 5%) 0%, rgb(255 255 255 / 10%) 100%);
    border-radius: 8px;
  }
}

/* 消息容器特有样式 */
.message-container {
  padding: 0;
  overflow-y: auto;

  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color-darker);
    border-radius: 3px;
    &:hover {
      background-color: var(--el-color-primary-light-5);
    }
  }
  &::-webkit-scrollbar-track {
    background-color: var(--el-border-color-lighter);
    border-radius: 3px;
  }
}
.message-card {
  margin-bottom: 16px;
  background: rgb(255 255 255 / 10%);
  backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgb(255 255 255 / 18%);
  border-radius: 12px;
  transition: all 0.3s ease;
  &:hover {
    background: rgb(255 255 255 / 20%);
    border-color: var(--el-color-primary-light-7);
    box-shadow: 0 8px 20px rgb(0 0 0 / 10%);
    transform: translateY(-3px);
  }
  .message-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    h4 {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
    .message-time {
      font-size: 13px;
      color: var(--el-text-color-secondary);
    }
  }
  .message-content {
    p {
      margin: 0;
      font-size: 14px;
      line-height: 1.6;
      color: var(--el-text-color-regular);
    }
  }
}
:deep(.el-timeline-item__node) {
  background-color: var(--el-color-primary-light-7);
  border-color: var(--el-color-primary);
}
:deep(.el-timeline-item__tail) {
  border-left-color: var(--el-border-color-lighter);
}
:deep(.el-timeline-item__timestamp) {
  font-size: 13px;
  color: var(--el-text-color-secondary);
}
