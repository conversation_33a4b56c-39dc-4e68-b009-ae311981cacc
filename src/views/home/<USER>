<template>
  <ResponsiveWrapper custom-class="main-box glass-container">
    <div class="main-content">
      <el-row :gutter="responsiveGutter">
        <!-- 左侧图表区域 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <el-card class="box-card glass-card">
            <template #header>
              <div class="clearfix">
                <span>{{ t("common.deviceStatistics") }}</span>
                <el-button type="primary" class="action-button refresh-button" :icon="Refresh" @click="refreshData">
                  {{ t("common.refresh") }}
                </el-button>
              </div>
            </template>
            <div class="chart-container" ref="chartRef"></div>
          </el-card>
        </el-col>
        <!-- 右侧消息区域 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <el-card class="box-card glass-card">
            <template #header>
              <div class="clearfix">
                <span>{{ t("common.messageCenter") }}</span>
                <el-button type="primary" class="action-button refresh-button" :icon="Refresh" @click="refreshMessages">
                  {{ t("common.refresh") }}
                </el-button>
              </div>
            </template>
            <div class="message-container">
              <el-empty v-if="!unreadMessage?.length" :description="t('common.noData')" />
              <el-timeline v-else>
                <el-timeline-item
                  v-for="(message, index) in unreadMessage"
                  :key="index"
                  :timestamp="message.creationTime"
                  type="primary"
                >
                  <el-card class="message-card glass-content-card">
                    <div class="message-header">
                      <h4>{{ isChinese ? message.title : message.titleEn || message.title }}</h4>
                      <span class="message-time">{{ formatTime(message.creationTime) }}</span>
                    </div>
                    <div class="message-content">
                      <p>{{ isChinese ? message.message : message.messageEn || message.message }}</p>
                    </div>
                  </el-card>
                </el-timeline-item>
              </el-timeline>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts" name="home">
import { ECOption } from "@/components/ECharts/config";
import { computed, onMounted, onBeforeUnmount, ref, watch } from "vue";
import { getUserDeviceStatistics } from "@/api/modules/home";
import * as echarts from "echarts";
import { useI18n } from "vue-i18n";
import { getUnreadMessages } from "@/api/modules/message";
import { useGlobalStore } from "@/stores/modules/global";
import { getDeviceType } from "@/api/modules/project";
import { Refresh } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { useResponsive } from "@/hooks/useResponsive";

const globalStore = useGlobalStore();
const isChinese = computed(() => globalStore.language === "zh");
const { t } = useI18n();

// 响应式布局
const { isMobile, isTablet } = useResponsive();

// 响应式间距
const responsiveGutter = computed(() => {
  if (isMobile.value) return 12;
  if (isTablet.value) return 16;
  return 20;
});

const yAxisData = ref([]);
const online = ref([]);
const offline = ref([]);
const chartRef = ref(null);
const unreadMessage = ref();
const deviceType = ref();
const originalData = ref(null);

// 监听语言变化
watch(isChinese, () => {
  if (originalData.value) {
    initChart(originalData.value);
  }
});

// 监听暗黑模式变化
watch(
  () => globalStore.isDark,
  () => {
    if (originalData.value) {
      // 给一点延迟，确保主题切换完成
      setTimeout(() => {
        initChart(originalData.value);
      }, 300);
    }
  }
);

const option: ECOption = {
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow"
    },
    backgroundColor: globalStore.isDark ? "rgba(30, 30, 30, 0.9)" : "rgba(255, 255, 255, 0.9)",
    borderColor: globalStore.isDark ? "rgba(255, 255, 255, 0.2)" : "rgba(0, 0, 0, 0.1)",
    textStyle: {
      color: globalStore.isDark ? "#ffffff" : "#333333"
    }
  },
  legend: {
    data: [t("common.onLine"), t("common.offLine")],
    textStyle: {
      // 使用变量确保在暗黑模式下有足够的对比度
      color: globalStore.isDark ? "#ffffff" : "#333333",
      fontWeight: globalStore.isDark ? "bold" : "normal",
      fontSize: globalStore.isDark ? 14 : 12,
      textShadow: globalStore.isDark ? "0 0 3px rgba(0, 0, 0, 0.5)" : "none"
    },
    // 在暗黑模式下添加背景色以增强对比度
    backgroundColor: globalStore.isDark ? "rgba(40, 40, 40, 0.8)" : "transparent",
    borderRadius: 4,
    padding: globalStore.isDark ? 8 : 5,
    itemGap: 15,
    borderWidth: globalStore.isDark ? 1 : 0,
    borderColor: globalStore.isDark ? "rgba(255, 255, 255, 0.2)" : "transparent"
  },
  grid: {
    left: "3%",
    right: "4%",
    bottom: "3%",
    containLabel: true
  },
  xAxis: {
    type: "value",
    axisLabel: {
      color: globalStore.isDark ? "#e0e0e0" : "#333333"
    },
    axisLine: {
      lineStyle: {
        color: globalStore.isDark ? "rgba(255, 255, 255, 0.3)" : "#ccc"
      }
    },
    splitLine: {
      lineStyle: {
        color: globalStore.isDark ? "rgba(255, 255, 255, 0.1)" : "#eee"
      }
    }
  },
  yAxis: {
    type: "category",
    data: yAxisData.value,
    axisLabel: {
      color: globalStore.isDark ? "#e0e0e0" : "#333333",
      fontWeight: globalStore.isDark ? "bold" : "normal"
    },
    axisLine: {
      lineStyle: {
        color: globalStore.isDark ? "rgba(255, 255, 255, 0.3)" : "#ccc"
      }
    }
  },
  series: [
    {
      name: t("common.onLine"),
      type: "bar",
      stack: "total",
      label: {
        show: true
      },
      emphasis: {
        focus: "series"
      },
      data: online.value
    },
    {
      name: t("common.offLine"),
      type: "bar",
      stack: "total",
      label: {
        show: true
      },
      emphasis: {
        focus: "series"
      },
      data: offline.value
    }
  ]
};

async function initChart(data?: any) {
  if (!chartRef.value) return;

  // 使用临时变量名，避免与全局变量冲突
  const existingChart = echarts.getInstanceByDom(chartRef.value);
  if (existingChart) {
    existingChart.dispose();
  }

  const userDeviceStatistics = data || (await getUserDeviceStatistics());
  if (userDeviceStatistics && userDeviceStatistics.code == "200" && Array.isArray(userDeviceStatistics.data)) {
    originalData.value = userDeviceStatistics;

    const originalYAxisData = [
      ...new Set(
        userDeviceStatistics.data.map((item: any) => {
          return item.deviceType;
        })
      )
    ];

    let convertedYAxisData = [...originalYAxisData];
    if (isChinese.value) {
      convertedYAxisData = originalYAxisData.map(item => {
        if (deviceType.value) {
          const deviceTypeItem = deviceType.value.find((deviceTypeItem: any) => deviceTypeItem.configCode === item);
          return deviceTypeItem ? deviceTypeItem.configDesc : item;
        }
        return item;
      });
    } else {
      convertedYAxisData = originalYAxisData.map(item => {
        if (deviceType.value) {
          const deviceTypeItem = deviceType.value.find((deviceTypeItem: any) => deviceTypeItem.configCode === item);
          return deviceTypeItem ? deviceTypeItem.attribute2 : item;
        }
        return item;
      });
    }

    yAxisData.value = convertedYAxisData;
    online.value = [];
    offline.value = [];

    originalYAxisData.forEach(type => {
      const onlineItem = userDeviceStatistics.data.find((item: any) => item.deviceType === type && item.status === 0);
      const offlineItem = userDeviceStatistics.data.find((item: any) => item.deviceType === type && item.status === 1);

      online.value.push(onlineItem ? onlineItem.deviceCount : 0);
      offline.value.push(offlineItem ? offlineItem.deviceCount : 0);
    });

    // 初始化图表，根据当前主题选择模式
    const newInstance = echarts.init(chartRef.value, globalStore.isDark ? "dark" : undefined);
    newInstance.setOption({
      ...option,
      legend: {
        ...option.legend,
        data: [t("common.onLine"), t("common.offLine")],
        textStyle: {
          color: globalStore.isDark ? "#ffffff" : "#333333",
          fontWeight: globalStore.isDark ? "bold" : "normal",
          fontSize: globalStore.isDark ? 14 : 12,
          textShadow: globalStore.isDark ? "0 0 3px rgba(0, 0, 0, 0.5)" : "none"
        },
        backgroundColor: globalStore.isDark ? "rgba(40, 40, 40, 0.8)" : "transparent",
        borderWidth: globalStore.isDark ? 1 : 0,
        borderColor: globalStore.isDark ? "rgba(255, 255, 255, 0.2)" : "transparent"
      },
      yAxis: {
        ...option.yAxis,
        data: yAxisData.value,
        axisLabel: {
          color: globalStore.isDark ? "#e0e0e0" : "#333333",
          fontWeight: globalStore.isDark ? "bold" : "normal"
        },
        axisLine: {
          lineStyle: {
            color: globalStore.isDark ? "rgba(255, 255, 255, 0.3)" : "#ccc"
          }
        }
      },
      xAxis: {
        ...option.xAxis,
        axisLabel: {
          color: globalStore.isDark ? "#e0e0e0" : "#333333"
        },
        axisLine: {
          lineStyle: {
            color: globalStore.isDark ? "rgba(255, 255, 255, 0.3)" : "#ccc"
          }
        },
        splitLine: {
          lineStyle: {
            color: globalStore.isDark ? "rgba(255, 255, 255, 0.1)" : "#eee"
          }
        }
      },
      series: [
        {
          ...option.series[0],
          name: t("common.onLine"),
          data: online.value
        },
        {
          ...option.series[1],
          name: t("common.offLine"),
          data: offline.value
        }
      ]
    });

    // 保存图表实例以便于后续清理
    chartInstance = newInstance;

    // 创建一个ResizeObserver来监听容器大小变化
    if (resizeObserver) {
      resizeObserver.disconnect();
    }

    resizeObserver = new ResizeObserver(() => {
      if (chartInstance) {
        chartInstance.resize();
      }
    });

    // 监听图表容器的大小变化
    if (chartRef.value) {
      resizeObserver.observe(chartRef.value);
    }
  }
}

// 刷新数据
const refreshData = async () => {
  await initChart();
  ElMessage.success(t("common.refreshSuccess"));
};

// 刷新消息
const refreshMessages = async () => {
  const result = await getUnreadMessages();
  if (result && result.code === "200") {
    unreadMessage.value = result.data;
  }
  ElMessage.success(t("common.refreshSuccess"));
};

// 格式化时间
const formatTime = (time: string) => {
  return time;
};

// 存储图表实例和监听器引用
// 注意：这些变量必须在函数之前声明，以便在整个组件中使用
let chartInstance = null;
let resizeObserver = null;
let resizeListener = null;

onMounted(async () => {
  const result = await getUnreadMessages();
  if (result && result.code === "200") {
    unreadMessage.value = result.data;
  }
  const typeResult = await getDeviceType();
  if (typeResult && typeResult.code === "200") {
    deviceType.value = typeResult.data;
  }
  await initChart();

  // 添加窗口调整大小的监听器
  resizeListener = () => {
    if (chartInstance) {
      chartInstance.resize();
    }
  };
  window.addEventListener("resize", resizeListener);
});

// 组件卸载前清理监听器
onBeforeUnmount(() => {
  // 清理ResizeObserver
  if (resizeObserver) {
    resizeObserver.disconnect();
  }

  // 清理窗口大小变化的监听器
  if (resizeListener) {
    window.removeEventListener("resize", resizeListener);
  }

  // 清理图表实例
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
});
</script>

<style scoped lang="scss">
@import "./index";
</style>
