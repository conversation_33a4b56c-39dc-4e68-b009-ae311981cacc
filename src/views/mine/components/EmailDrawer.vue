<template>
  <el-drawer
    v-model="drawerVisible"
    :destroy-on-close="true"
    size="680px"
    :title="`${drawerProps.title} ${t('user.email')}`"
    @close="onDrawerClose"
    class="glass-dialog"
  >
    <el-steps :active="currentStep" simple>
      <el-step :title="t('user.verifyIdentity')" icon="el-icon-edit"> </el-step>
      <el-step :title="t('user.setNewEmail')" icon="el-icon-upload"></el-step>
    </el-steps>
    <div v-if="currentStep === 0 || currentStep === 1">
      <el-form
        ref="emailFormRef"
        label-width="100px"
        label-suffix=" :"
        :rules="formRules"
        :model="drawerProps.row"
        :hide-required-asterisk="drawerProps.isView"
      >
        <el-form-item :label="t('user.email')" prop="email" v-if="(originalEmail && currentStep === 0) || currentStep === 1">
          <el-input v-model="drawerProps.row.email" :placeholder="t('user.emailTip')" />
        </el-form-item>
        <el-form-item :label="t('user.code')" prop="code" v-if="(originalEmail && currentStep === 0) || currentStep === 1">
          <el-input v-model="drawerProps.row.code" :placeholder="t('user.codeTip')">
            <template #append>
              <el-button :disabled="!drawerProps.row.email || sendCodeLoading || countdown > 0" @click="sendCode">
                {{ countdown > 0 ? $t("user.resendCodeTime", { time: countdown }) : $t("user.sendCode") }}
              </el-button>
            </template>
          </el-input>
        </el-form-item>
        <verification-code
          v-model="drawerProps.row.code"
          :value="drawerProps.row.email"
          :original-value="originalEmail"
          :step="currentStep"
          type="email"
          @update:model-value="updateCode"
          style="display: none"
        />
        <el-form-item :label="t('user.password')" prop="password" v-if="!originalEmail && currentStep === 0">
          <el-input
            v-model="drawerProps.row.password"
            type="password"
            :placeholder="t('device.passwordPlaceholder')"
            clearable
          ></el-input>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="drawerVisible = false">{{ $t("common.cancel") }}</el-button>
      <el-button v-show="!drawerProps.isView" type="primary" :loading="loading" @click="handleSubmit">
        {{ $t("common.confirm") }}
      </el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts" name="EmailDrawer">
import { ref, computed } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { useI18n } from "vue-i18n";
import { useUserStore } from "@/stores/modules/user";
import type { Project } from "@/api/interface/project";
import { updateEmail, validationVerificationCode, sendEmailCode } from "@/api/modules/mine";
import { logoutApi, isRegisterApi } from "@/api/modules/login";
import { useRouter } from "vue-router";
import { LOGIN_URL } from "@/config";
import VerificationCode from "@/components/VerificationCode/index.vue";
import { getPasswordRules, getEmailRules, getCodeRules } from "@/utils/formValidation";

const { t } = useI18n();
const router = useRouter();
const userStore = useUserStore();
const drawerVisible = ref(false);
const currentStep = ref(0);
const loading = ref(false);
const sendCodeLoading = ref(false);
const originalEmail = ref("");
const countdown = ref(0);
const countdownTimer = ref<number | null>(null);

interface DrawerProps {
  title: string;
  isView: boolean;
  row: Partial<any>;
  parentRow: Partial<Project.ResDeviceList>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
  refreshParent?: () => void; // 添加更通用的刷新父页面的回调函数
  onSuccess?: () => void; // 添加成功回调函数
}

const drawerProps = ref<DrawerProps>({
  isView: false,
  title: "",
  row: {
    email: "",
    code: ""
  },
  parentRow: {}
});

// 接收父组件传过来的参数
const acceptParams = (params: DrawerProps) => {
  // 不要使用 JSON.parse(JSON.stringify(params))，因为这会丢失函数类型的属性
  // 正确的方式是使用对象解构或属性赋值
  drawerProps.value = { ...params }; // 使用对象解构来复制参数，保留函数属性

  // 记录原始邮箱值
  originalEmail.value = params.row.email || "";

  // 初始化表单数据
  drawerProps.value.row = { ...params.row }; // 复制 row 对象
  drawerProps.value.row.email = "";
  drawerProps.value.row.password = null;

  // 打开抽屉
  drawerVisible.value = true;
};

// 提交数据（新增/编辑）
const emailFormRef = ref<FormInstance>();

// 更新验证码方法
const updateCode = (value: string) => {
  drawerProps.value.row.code = value;
};

// 开始倒计时函数
const startCountdown = () => {
  // 清除之前的定时器
  if (countdownTimer.value) {
    window.clearInterval(countdownTimer.value);
  }

  // 设置倒计时初始值为180秒（3分钟）
  countdown.value = 180;

  // 创建定时器，每秒减1
  countdownTimer.value = window.setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--;
    } else {
      // 倒计时结束，清除定时器
      if (countdownTimer.value) {
        window.clearInterval(countdownTimer.value);
        countdownTimer.value = null;
      }
    }
  }, 1000);
};

// 发送验证码
const sendCode = async () => {
  if (!drawerProps.value.row.email) {
    ElMessage.warning({ message: t("user.emailNotEmpty") });
    return;
  }

  // 第一步验证时，检查是否是当前用户的邮箱
  if (currentStep.value === 0 && originalEmail.value && drawerProps.value.row.email !== originalEmail.value) {
    ElMessage.warning({ message: t("user.notCurrentUserEmail") });
    return;
  }

  // 第二步设置新值时，检查是否已被其他用户绑定
  if (currentStep.value === 1) {
    try {
      const isExist = await isRegisterApi(drawerProps.value.row.email);
      if (isExist && isExist.code !== "200") {
        ElMessage.warning({ message: t("user.emailBeenBind") });
        return;
      }
    } catch (error) {
      console.error("检查邮箱是否存在失败:", error);
      ElMessage.error({ message: t("common.requestFailed") });
      return;
    }
  }

  sendCodeLoading.value = true;
  try {
    const response = await sendEmailCode({ email: drawerProps.value.row.email });

    if (!response || response.code !== "200") {
      ElMessage.error({ message: response?.msg || t("user.sendCodeFail") });
      return;
    }

    ElMessage.success({ message: t("user.sendCodeSuccess") + ", " + t("user.codeValidTime") });
    startCountdown();
  } catch (error) {
    console.error("发送验证码失败:", error);
    ElMessage.error({ message: t("user.sendCodeFail") });
  } finally {
    sendCodeLoading.value = false;
  }
};

// 动态生成表单验证规则
const formRules = computed(() => {
  if (currentStep.value === 0 && !originalEmail.value) {
    // 密码验证规则
    return getPasswordRules();
  } else {
    // 邮箱和验证码规则
    return {
      ...getEmailRules(),
      ...getCodeRules()
    };
  }
});

const handleSubmit = async () => {
  // 表单验证
  if (!emailFormRef.value) return;
  const valid = await emailFormRef.value.validate().catch(() => false);
  if (!valid) return;

  loading.value = true;
  try {
    let params: any;
    let apiFunction: any;

    // 动态生成参数和 API 函数
    if (currentStep.value === 0) {
      // 验证旧邮箱或密码
      if (originalEmail.value) {
        params = { email: drawerProps.value.row.email, code: drawerProps.value.row.code };
      } else {
        params = { password: drawerProps.value.row.password };
      }
      apiFunction = () => validationVerificationCode(params);
    } else {
      // 更新邮箱
      if (originalEmail.value === drawerProps.value.row.email) {
        ElMessage.warning({ message: t("user.emailSame") });
        loading.value = false;
        return;
      }
      params = { email: drawerProps.value.row.email, code: drawerProps.value.row.code };
      apiFunction = () => updateEmail(params);
    }

    const response = await apiFunction();

    if (!response || response.code !== "200") {
      ElMessage.error({ message: response?.msg || t("common.requestFailed") });
      return;
    }

    if (currentStep.value === 0) {
      // 第一步验证成功，进入第二步
      currentStep.value = 1;
      // 重置表单数据
      drawerProps.value.row.email = "";
      drawerProps.value.row.code = "";

      // 重置倒计时
      if (countdownTimer.value) {
        window.clearInterval(countdownTimer.value);
        countdownTimer.value = null;
      }
      countdown.value = 0;
    } else {
      // 第二步成功，显示成功消息并关闭
      ElMessage({
        message: t("user.setNewEmailSuccess"),
        type: "success",
        duration: 3000,
        onClose: async () => {
          drawerVisible.value = false; // 自动关闭弹窗

          // 邮箱修改成功后，执行退出登录操作
          try {
            // 1.执行退出登录接口
            await logoutApi();

            // 2.清除 Token
            userStore.setToken("");

            // 3.重定向到登录页
            await router.replace(LOGIN_URL);
            ElMessage.success(t("header.logoutSuccess"));

            // 如果提供了成功回调函数，则调用它
            if (drawerProps.value.onSuccess) {
              try {
                await drawerProps.value.onSuccess();
              } catch (callbackError) {
                console.error("回调函数执行出错:", callbackError);
              }
            }
          } catch (error) {
            console.error("退出登录失败:", error);
          }
        }
      });
    }
  } catch (error) {
    console.error("提交表单出错:", error);
    ElMessage.error(t("common.requestFailed"));
  } finally {
    loading.value = false;
  }
};

// 监听关闭事件
const onDrawerClose = () => {
  drawerProps.value.row = {};
  currentStep.value = 0;
  loading.value = false;
  sendCodeLoading.value = false;

  // 清除倒计时定时器
  if (countdownTimer.value) {
    window.clearInterval(countdownTimer.value);
    countdownTimer.value = null;
  }
  countdown.value = 0;
};

defineExpose({
  acceptParams
});
</script>
<style lang="scss" scoped>
.glass-dialog {
  :deep(.el-drawer__header) {
    padding: 15px 20px;
    margin-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
  }
  :deep(.el-steps) {
    margin-bottom: 30px;
  }
  :deep(.el-form) {
    max-width: 500px;
    margin: 0 auto;
  }
  :deep(.el-form-item__content) {
    width: 100%;
  }
}
</style>
