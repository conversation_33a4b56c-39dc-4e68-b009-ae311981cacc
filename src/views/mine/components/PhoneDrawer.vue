<template>
  <el-drawer
    v-model="drawerVisible"
    :destroy-on-close="true"
    :title="`${drawerProps.title} ${t('user.phone')}`"
    class="glass-dialog phone-drawer"
    :size="drawerSize"
    @close="onDrawerClose"
  >
    <el-steps :active="currentStep" simple>
      <el-step :title="t('user.verifyIdentity')" icon="el-icon-edit"></el-step>
      <el-step :title="t('user.setNewPhone')" icon="el-icon-upload"></el-step>
    </el-steps>
    <div v-if="(currentStep === 0 && originalPhone) || currentStep === 1">
      <el-form
        ref="phoneFormRef"
        :hide-required-asterisk="drawerProps.isView"
        :model="drawerProps.row"
        :rules="formRules"
        label-suffix=" :"
        label-width="100px"
      >
        <el-form-item :label="t('user.phone')" prop="phone">
          <el-input v-model="drawerProps.row.phone" :placeholder="t('user.phoneTip')" />
        </el-form-item>
        <el-form-item :label="t('user.code')" prop="code">
          <el-input v-model="drawerProps.row.code" :placeholder="t('user.codeTip')">
            <template #append>
              <el-button :disabled="!drawerProps.row.phone || sendCodeLoading || countdown > 0" @click="sendCode">
                {{ countdown > 0 ? $t("user.resendCodeTime", { time: countdown }) : $t("user.sendCode") }}
              </el-button>
            </template>
          </el-input>
        </el-form-item>
        <verification-code
          v-model="drawerProps.row.code"
          :value="drawerProps.row.phone"
          :original-value="originalPhone"
          :step="currentStep"
          type="phone"
          @update:model-value="updateCode"
          style="display: none"
        />
      </el-form>
    </div>
    <div v-if="currentStep === 0 && !originalPhone">
      <el-form
        ref="phoneFormRef"
        :hide-required-asterisk="drawerProps.isView"
        :model="drawerProps.row"
        :rules="formRules"
        label-suffix=" :"
        label-width="100px"
      >
        <el-form-item :label="t('user.password')" prop="password">
          <el-input
            v-model="drawerProps.row.password"
            :placeholder="t('device.passwordPlaceholder')"
            clearable
            type="password"
          ></el-input>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="drawerVisible = false">{{ $t("common.cancel") }}</el-button>
      <el-button v-show="!drawerProps.isView" type="primary" :loading="loading" @click="handleSubmit">
        {{ $t("common.confirm") }}
      </el-button>
    </template>
  </el-drawer>
</template>

<script lang="ts" name="PhoneDrawer" setup>
import { computed, ref } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { useI18n } from "vue-i18n";
import { useUserStore } from "@/stores/modules/user";
import { updatePhone, validationVerificationCode, sendPhoneCode } from "@/api/modules/mine";
import { logoutApi, isRegisterApi } from "@/api/modules/login";
import { useRouter } from "vue-router";
import { LOGIN_URL } from "@/config";
import VerificationCode from "@/components/VerificationCode/index.vue";
import { getPasswordRules, getPhoneRules, getCodeRules } from "@/utils/formValidation";
import { useResponsive } from "@/hooks/useResponsive";

const { t } = useI18n();
const router = useRouter();
const userStore = useUserStore();

// 响应式布局
const { isMobile, isTablet } = useResponsive();

// 响应式 Drawer 尺寸
const drawerSize = computed(() => {
  if (isMobile.value) return "100%";
  if (isTablet.value) return "90%";
  return "680px";
});

const drawerVisible = ref(false);
const currentStep = ref(0);
const loading = ref(false);
const sendCodeLoading = ref(false);
const countdown = ref(0);
const countdownTimer = ref<number | null>(null);

interface DrawerProps {
  title: string;
  isView: boolean;
  row: Partial<any>;
  parentRow?: Partial<any>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const drawerProps = ref<DrawerProps>({
  isView: false,
  title: "",
  row: {
    phone: "",
    code: "",
    password: ""
  }
});

const originalPhone = ref("");

// 接收父组件传过来的参数
const acceptParams = (params: DrawerProps) => {
  console.log("acceptParams params:", params);
  params.row.password = "";
  drawerProps.value = JSON.parse(JSON.stringify(params));
  originalPhone.value = params.row.phone || ""; // 记录原始邮手机号
  drawerProps.value.row.phone = "";
  // drawerProps.value = params;
  drawerVisible.value = true;
};

// 提交数据（新增/编辑）
const phoneFormRef = ref<FormInstance>();

// 更新验证码方法
const updateCode = (value: string) => {
  drawerProps.value.row.code = value;
};

// 开始倒计时函数
const startCountdown = () => {
  // 清除之前的定时器
  if (countdownTimer.value) {
    window.clearInterval(countdownTimer.value);
  }

  // 设置倒计时初始值为180秒（3分钟）
  countdown.value = 180;

  // 创建定时器，每秒减1
  countdownTimer.value = window.setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--;
    } else {
      // 倒计时结束，清除定时器
      if (countdownTimer.value) {
        window.clearInterval(countdownTimer.value);
        countdownTimer.value = null;
      }
    }
  }, 1000);
};

// 发送验证码
const sendCode = async () => {
  if (!drawerProps.value.row.phone) {
    ElMessage.warning({ message: t("user.phoneNotEmpty") });
    return;
  }

  // 第一步验证时，检查是否是当前用户的手机号
  if (currentStep.value === 0 && originalPhone.value && drawerProps.value.row.phone !== originalPhone.value) {
    ElMessage.warning({ message: t("user.notCurrentUserPhone") });
    return;
  }

  // 第二步设置新值时，检查是否已被其他用户绑定
  if (currentStep.value === 1) {
    try {
      const isExist = await isRegisterApi(drawerProps.value.row.phone);
      if (isExist && isExist.code !== "200") {
        ElMessage.warning({ message: t("user.phoneBeenBind") });
        return;
      }
    } catch (error) {
      console.error("检查手机号是否存在失败:", error);
      ElMessage.error({ message: t("common.requestFailed") });
      return;
    }
  }

  sendCodeLoading.value = true;
  try {
    const response = await sendPhoneCode({ phone: drawerProps.value.row.phone });

    if (!response || response.code !== "200") {
      ElMessage.error({ message: response?.msg || t("user.sendCodeFail") });
      return;
    }

    ElMessage.success({ message: t("user.sendCodeSuccess") + ", " + t("user.codeValidTime") });
    startCountdown();
  } catch (error) {
    console.error("发送验证码失败:", error);
    ElMessage.error({ message: t("user.sendCodeFail") });
  } finally {
    sendCodeLoading.value = false;
  }
};

// 动态生成表单验证规则
const formRules = computed(() => {
  if (currentStep.value === 0 && !originalPhone.value) {
    // 密码验证规则
    return getPasswordRules();
  } else {
    // 手机号和验证码规则
    return {
      ...getPhoneRules(),
      ...getCodeRules()
    };
  }
});

const handleSubmit = async () => {
  // 表单验证
  if (!phoneFormRef.value) return;
  const valid = await phoneFormRef.value.validate().catch(() => false);
  if (!valid) return;

  loading.value = true;
  try {
    let params: any;
    let apiFunction: any;

    // 动态生成参数和 API 函数
    if (currentStep.value === 0) {
      // 验证旧手机号或密码
      if (drawerProps.value.row.phone) {
        params = { phone: drawerProps.value.row.phone, code: drawerProps.value.row.code };
        apiFunction = () => validationVerificationCode(params);
      } else {
        params = { password: drawerProps.value.row.password };
        apiFunction = () => validationVerificationCode(params);
      }
    } else {
      // 更新手机号
      params = { phone: drawerProps.value.row.phone, code: drawerProps.value.row.code };
      apiFunction = () => updatePhone(params);
    }

    const response = await apiFunction();

    if (!response || response.code !== "200") {
      ElMessage.error({ message: response?.msg || t("common.requestFailed") });
      return;
    }

    if (currentStep.value === 0) {
      // 第一步验证成功，进入第二步
      currentStep.value = 1;
      // 重置表单数据
      drawerProps.value.row.phone = "";
      drawerProps.value.row.code = "";

      // 重置倒计时
      if (countdownTimer.value) {
        window.clearInterval(countdownTimer.value);
        countdownTimer.value = null;
      }
      countdown.value = 0;
    } else {
      // 第二步成功，显示成功消息并关闭
      ElMessage({
        message: t("user.setNewPhoneSuccess"),
        type: "success",
        duration: 3000,
        onClose: async () => {
          drawerVisible.value = false; // 自动关闭弹窗

          // 手机号修改成功后，执行退出登录操作
          try {
            // 1.执行退出登录接口
            await logoutApi();

            // 2.清除 Token
            userStore.setToken("");

            // 3.重定向到登录页
            await router.replace(LOGIN_URL);
            ElMessage.success(t("header.logoutSuccess"));
          } catch (error) {
            console.error("退出登录失败:", error);
          }
        }
      });
    }
  } catch (error) {
    console.error("提交表单出错:", error);
    ElMessage.error(t("common.requestFailed"));
  } finally {
    loading.value = false;
  }
};

// 监听关闭事件
const onDrawerClose = () => {
  drawerProps.value.row = {};
  currentStep.value = 0;
  loading.value = false;
  sendCodeLoading.value = false;

  // 清除倒计时定时器
  if (countdownTimer.value) {
    window.clearInterval(countdownTimer.value);
    countdownTimer.value = null;
  }
  countdown.value = 0;
};

defineExpose({
  acceptParams
});
</script>
<style lang="scss" scoped>
@import "@/styles/mixins/responsive.scss";

.glass-dialog.phone-drawer {
  :deep(.el-drawer__header) {
    padding: 15px 20px;
    margin-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;

    @include respond-to(md) {
      padding: 12px 16px;
      margin-bottom: 16px;
    }

    @include respond-to(sm) {
      padding: 10px 12px;
      margin-bottom: 12px;
    }
  }

  :deep(.el-drawer__body) {
    @include respond-to(md) {
      padding: 16px;
    }

    @include respond-to(sm) {
      padding: 12px;
    }
  }

  :deep(.el-steps) {
    margin-bottom: 30px;

    @include respond-to(md) {
      margin-bottom: 20px;

      .el-step__title {
        font-size: var(--font-size-sm);
      }
    }

    @include respond-to(sm) {
      margin-bottom: 16px;

      .el-step__title {
        font-size: var(--font-size-xs);
      }
    }
  }

  :deep(.el-form) {
    max-width: 500px;
    margin: 0 auto;

    @include respond-to(md) {
      max-width: 100%;

      .el-form-item {
        margin-bottom: var(--spacing-md);

        .el-form-item__label {
          display: block !important;
          text-align: left !important;
          margin-bottom: var(--spacing-xs) !important;
          font-size: var(--font-size-sm) !important;
          width: 100% !important;
        }

        .el-form-item__content {
          margin-left: 0 !important;
          width: 100%;

          .el-input {
            width: 100% !important;

            .el-input__wrapper {
              font-size: var(--font-size-sm) !important;
            }

            .el-input-group__append {
              .el-button {
                font-size: var(--font-size-xs) !important;
                padding: 8px 12px !important;
              }
            }
          }
        }
      }
    }

    @include respond-to(sm) {
      .el-form-item {
        margin-bottom: 12px;

        .el-form-item__label {
          font-size: var(--font-size-xs) !important;
        }

        .el-form-item__content {
          .el-input {
            .el-input__wrapper {
              font-size: var(--font-size-xs) !important;
            }

            .el-input-group__append {
              .el-button {
                font-size: 11px !important;
                padding: 6px 8px !important;
              }
            }
          }
        }
      }
    }
  }
}
</style>
