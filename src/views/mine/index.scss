// 反馈对话框样式
.feedback-dialog {
  :deep(.el-dialog) {
    overflow: hidden;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgb(0 0 0 / 12%);
    transition:
      transform 0.3s ease,
      box-shadow 0.3s ease;
    transform: translateY(0);
    &:hover {
      box-shadow: 0 20px 40px rgb(0 0 0 / 15%);
      transform: translateY(-5px);
    }
    .el-dialog__header {
      position: relative;
      padding: 24px 30px;
      margin: 0;
      background: var(--el-bg-color-overlay);
      border-bottom: 1px solid var(--el-border-color-lighter);
      &::after {
        position: absolute;
        bottom: -1px;
        left: 50%;
        width: 80px;
        height: 3px;
        content: "";
        background: linear-gradient(90deg, var(--el-color-primary), var(--el-color-primary-light-5));
        border-radius: 3px;
        transform: translateX(-50%);
      }
      .el-dialog__title {
        font-size: 20px;
        font-weight: 600;
        color: var(--el-color-primary);
        letter-spacing: 0.5px;
      }
    }
    .el-dialog__body {
      padding: 30px;
      .el-form {
        width: 100%;
        .el-form-item {
          margin-bottom: 28px;
          &:last-child {
            margin-bottom: 0;
          }
          .el-form-item__label {
            padding-bottom: 10px;
            font-size: 15px;
            font-weight: 500;
            color: var(--el-text-color-regular);
            letter-spacing: 0.3px;
          }
          .el-input__wrapper {
            width: 100%;
            padding: 2px 15px;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgb(0 0 0 / 5%);
            transition: all 0.3s ease;
            &:hover {
              box-shadow: 0 5px 15px rgb(0 0 0 / 8%);
              transform: translateY(-2px);
            }
            &.is-focus {
              box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
            }
          }
          .el-textarea__inner {
            width: 100%;
            min-height: 140px;
            padding: 15px;
            font-size: 15px;
            line-height: 1.6;
            resize: vertical;
            border-radius: 12px;
            box-shadow: 0 3px 10px rgb(0 0 0 / 5%);
            transition: all 0.3s ease;
            &:hover {
              box-shadow: 0 5px 15px rgb(0 0 0 / 8%);
            }
            &:focus {
              box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
            }
          }
        }
        .form-foot {
          display: flex;
          gap: 15px;
          justify-content: flex-end;
          margin-top: 40px;
          .el-button {
            padding: 12px 28px;
            font-weight: 500;
            border-radius: 12px;
            transition: all 0.3s ease;
            &:hover {
              box-shadow: 0 8px 15px rgb(0 0 0 / 10%);
              transform: translateY(-3px);
            }
            &.el-button--primary {
              background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
              border: none;
              &:hover {
                background: linear-gradient(135deg, var(--el-color-primary-light-3), var(--el-color-primary));
              }
            }
          }
        }
      }
    }
  }
}

// 用户信息页样式
.profile-container {
  position: relative;

  /* 基本容器样式 */
  display: flex;
  justify-content: center;
  width: 100%;
  min-height: calc(100vh - 60px);
  padding: 30px 20px;
  overflow: hidden;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4ecf7 50%, #d3e0f7 100%);
  border-radius: 24px;
  transition: all 0.5s ease;

  /* 装饰元素样式 */
  .decoration-element {
    position: absolute;
    z-index: 0;
    pointer-events: none;
    filter: blur(40px);
    border-radius: 50%;
  }
  .decoration-top-right {
    top: -100px;
    right: -100px;
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgb(100 149 237 / 30%) 0%, rgb(100 149 237 / 0%) 70%);
    animation: float 20s ease-in-out infinite alternate;
  }
  .decoration-bottom-left {
    bottom: -150px;
    left: -150px;
    width: 500px;
    height: 500px;
    background: radial-gradient(circle, rgb(144 238 144 / 30%) 0%, rgb(144 238 144 / 0%) 70%);
    animation: float 25s ease-in-out infinite alternate-reverse;
  }
  .decoration-circle-1 {
    top: 20%;
    left: 10%;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgb(255 182 193 / 20%) 0%, rgb(255 182 193 / 0%) 70%);
    animation: pulse 8s ease-in-out infinite;
  }
  .decoration-circle-2 {
    right: 10%;
    bottom: 15%;
    width: 180px;
    height: 180px;
    background: radial-gradient(circle, rgb(255 215 0 / 20%) 0%, rgb(255 215 0 / 0%) 70%);
    animation: pulse 12s ease-in-out infinite reverse;
  }

  /* 内容区域样式 */
  .profile-content {
    z-index: 1;
    display: flex;
    flex-direction: column;
    gap: 24px;
    width: 100%;
    max-width: 800px;
    padding: 10px;
  }

  /* 卡片基本样式 */
  .profile-card {
    position: relative;
    padding: 30px;
    overflow: hidden;
    background: rgb(255 255 255 / 70%);
    backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border: 1px solid rgb(255 255 255 / 50%);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgb(0 0 0 / 10%);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    &:hover {
      border-color: rgba(var(--el-color-primary-rgb), 0.3);
      box-shadow: 0 15px 40px rgb(0 0 0 / 15%);
      transform: translateY(-5px);
    }

    /* 卡片光效 */
    &::before {
      position: absolute;
      top: -50%;
      left: -50%;
      z-index: 0;
      width: 200%;
      height: 200%;
      content: "";
      background: radial-gradient(circle, rgb(255 255 255 / 10%) 0%, rgb(255 255 255 / 0%) 80%);
      opacity: 0;
      transition: all 0.5s ease;
      transform: rotate(30deg);
    }
    &:hover::before {
      opacity: 1;
      animation: shine 3s infinite linear;
    }

    /* 标题样式 */
    .section-title {
      position: relative;
      padding-bottom: 10px;
      margin-bottom: 20px;
      font-size: 18px;
      font-weight: 600;
      color: var(--el-color-primary);
      border-bottom: 1px solid rgba(var(--el-color-primary-rgb), 0.1);
      &::after {
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 60px;
        height: 3px;
        content: "";
        background: linear-gradient(90deg, var(--el-color-primary), var(--el-color-primary-light-5));
        border-radius: 3px;
        transition: width 0.3s ease;
      }
    }
    &:hover .section-title::after {
      width: 100px;
    }
  }

  /* 主卡片样式 */
  .main-card {
    .profile-header {
      margin-bottom: 30px;
    }
    .avatar-container {
      display: flex;
      flex-direction: column;
      gap: 20px;
      align-items: center;
      justify-content: center;
      margin-bottom: 20px;
      text-align: center;
    }
    .avatar-wrapper {
      position: relative;
      flex-shrink: 0;
      .el-avatar {
        position: relative;
        z-index: 2;
        width: 120px;
        height: 120px;
        margin-bottom: 5px;
        background: rgb(255 255 255 / 25%);
        border: 5px solid rgb(255 255 255 / 80%);
        box-shadow: 0 10px 30px rgb(0 0 0 / 15%);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        &:hover {
          border-color: var(--el-color-primary-light-5);
          box-shadow: 0 15px 35px rgb(0 0 0 / 20%);
          transform: scale(1.05) rotate(5deg);
        }
      }
      .avatar-status {
        position: absolute;
        right: 10px;
        bottom: 10px;
        z-index: 3;
        width: 22px;
        height: 22px;
        background-color: #67c23a;
        border: 3px solid #ffffff;
        border-radius: 50%;
        box-shadow: 0 0 0 2px rgb(103 194 58 / 30%);
        animation: pulse 2s infinite;
      }
    }
    .user-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      text-align: center;
      .user-name {
        margin: 0 0 10px;
        font-size: 24px;
        font-weight: 600;
        color: var(--el-color-primary-dark-2);
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        &.empty {
          font-size: 20px;
          font-style: italic;
          color: var(--el-text-color-secondary);
        }
      }
      .user-role {
        display: flex;
        justify-content: center;
        margin-top: 5px;
        .el-tag {
          padding: 6px 14px;
          font-size: 13px;
          color: var(--el-color-success);
          background: rgba(var(--el-color-success-rgb), 0.1);
          border-color: rgba(var(--el-color-success-rgb), 0.2);
          border-radius: 20px;
          transition: all 0.3s ease;
          &:hover {
            background: rgba(var(--el-color-success-rgb), 0.15);
            box-shadow: 0 5px 15px rgba(var(--el-color-success-rgb), 0.2);
            transform: translateY(-2px);
          }
          .el-icon {
            margin-right: 5px;
            font-size: 14px;
          }
        }
      }
    }

    /* 信息项样式 */
    .profile-section {
      padding-top: 10px;
    }
    .info-item {
      display: flex;
      align-items: center;
      padding: 15px;
      margin-bottom: 20px;
      background: rgb(255 255 255 / 30%);
      border: 1px solid rgb(255 255 255 / 30%);
      border-radius: 12px;
      transition: all 0.3s ease;
      &:hover {
        background: rgb(255 255 255 / 40%);
        border-color: rgba(var(--el-color-primary-rgb), 0.2);
        box-shadow: 0 10px 20px rgb(0 0 0 / 5%);
        transform: translateY(-3px);
      }
      &:last-child {
        margin-bottom: 0;
      }
      .info-label {
        flex-shrink: 0;
        width: 80px;
        font-size: 15px;
        font-weight: 500;
        color: var(--el-text-color-regular);
      }
      .info-content {
        flex-grow: 1;
        padding: 0 15px;
        .info-value {
          display: flex;
          gap: 10px;
          align-items: center;
          font-size: 16px;
          color: var(--el-text-color-primary);
          .el-icon {
            font-size: 18px;
            color: var(--el-color-primary);
          }
          &.empty {
            font-style: italic;
            color: var(--el-text-color-secondary);
          }
        }
        .input-field {
          width: 100%;
          .el-input__wrapper {
            background: rgb(255 255 255 / 50%);
            border-radius: 8px;
            box-shadow: 0 3px 10px rgb(0 0 0 / 5%);
            transition: all 0.3s ease;
            &:hover {
              box-shadow: 0 5px 15px rgb(0 0 0 / 8%);
              transform: translateY(-2px);
            }
            &.is-focus {
              box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
            }
          }
          .input-icon {
            font-size: 16px;
            color: var(--el-color-primary);
          }
        }
      }
      .info-action {
        display: flex;
        flex-shrink: 0;
        justify-content: center;
        width: 40px;
        .action-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 36px;
          height: 36px;
          padding: 0;
          border-radius: 50%;
          transition: all 0.3s ease;
          .el-icon {
            font-size: 16px;
          }
          &:hover {
            box-shadow: 0 5px 15px rgba(var(--el-color-primary-rgb), 0.2);
            transform: translateY(-2px) scale(1.05);
          }
        }
      }
    }
  }

  /* 快捷链接卡片样式 */
  .links-card {
    .quick-links {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20px;

      @media (width <= 768px) {
        grid-template-columns: repeat(2, 1fr);
      }

      @media (width <= 480px) {
        grid-template-columns: 1fr;
      }
    }
    .link-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 120px;
      padding: 20px;
      cursor: pointer;
      background: rgb(255 255 255 / 30%);
      border: 1px solid rgb(255 255 255 / 30%);
      border-radius: 16px;
      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      &:hover {
        background: rgb(255 255 255 / 40%);
        border-color: rgba(var(--el-color-primary-rgb), 0.3);
        box-shadow: 0 15px 30px rgb(0 0 0 / 10%);
        transform: translateY(-5px);
      }
      .link-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        margin-bottom: 12px;
        background: rgba(var(--el-color-primary-rgb), 0.1);
        border-radius: 50%;
        transition: all 0.3s ease;
        .el-icon {
          font-size: 24px;
          color: var(--el-color-primary);
        }
      }
      .link-text {
        font-size: 15px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        text-align: center;
        transition: all 0.3s ease;
      }
      &:hover {
        .link-icon {
          background: rgba(var(--el-color-primary-rgb), 0.2);
          transform: scale(1.1) rotate(5deg);
        }
        .link-text {
          color: var(--el-color-primary);
        }
      }
    }
  }

  @media (width <= 768px) {
    padding: 15px;
  }

  /* 退出登录按钮样式 */
  .logout-container {
    display: flex;
    justify-content: center;
    margin-top: 10px;
    margin-bottom: 20px;
    .logout-button {
      position: relative;
      min-width: 200px;
      padding: 12px 30px;
      overflow: hidden;
      font-size: 16px;
      font-weight: 600;
      color: #ffffff;
      background: linear-gradient(45deg, #ff5252, #ff7675);
      border: none;
      border-radius: 50px;
      box-shadow: 0 10px 20px rgb(255 82 82 / 30%);
      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      &::after {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        content: "";
        background: linear-gradient(45deg, transparent, rgb(255 255 255 / 20%), transparent);
        transition: transform 0.6s ease;
        transform: translateX(-100%);
      }
      .el-icon {
        margin-right: 10px;
        font-size: 18px;
      }
      &:hover {
        background: linear-gradient(45deg, #ff3838, #ff5252);
        box-shadow: 0 15px 30px rgb(255 82 82 / 40%);
        transform: translateY(-5px);
        &::after {
          transform: translateX(100%);
        }
      }
      &:active {
        box-shadow: 0 5px 15px rgb(255 82 82 / 40%);
        transform: translateY(2px);
      }
    }
  }
}

/* 动画关键帧 */
@keyframes float {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
  100% {
    transform: translateY(0) rotate(0deg);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.05);
  }
  100% {
    opacity: 0.4;
    transform: scale(1);
  }
}

@keyframes shine {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// 对话框样式
:deep(.el-dialog) {
  width: 90% !important;
  max-width: 800px;
  overflow: hidden;

  /* 毕玻璃效果 */
  background: rgb(255 255 255 / 70%);
  backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgb(255 255 255 / 18%);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgb(0 0 0 / 10%);
  .el-dialog__header {
    padding: 20px 24px;
    margin: 0;
    background: rgb(255 255 255 / 10%);
    border-bottom: 1px solid rgb(255 255 255 / 20%);
  }
  .el-dialog__body {
    padding: 24px;
    background: rgb(255 255 255 / 5%);
  }
  .el-dialog__footer {
    padding: 16px 24px;
    background: rgb(255 255 255 / 10%);
    border-top: 1px solid rgb(255 255 255 / 20%);
  }
}

/* 黑暗模式样式 */
html.dark {
  .profile-container {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d3748 70%, #364156 100%);
    box-shadow: 0 10px 30px rgb(0 0 0 / 20%);

    /* 装饰元素 - 黑暗模式 */
    .decoration-top-right {
      background: radial-gradient(circle, rgb(255 100 100 / 30%) 0%, rgb(255 100 100 / 0%) 70%);
    }
    .decoration-bottom-left {
      background: radial-gradient(circle, rgb(100 200 100 / 30%) 0%, rgb(100 200 100 / 0%) 70%);
    }
    .decoration-circle-1 {
      background: radial-gradient(circle, rgb(100 149 237 / 30%) 0%, rgb(100 149 237 / 0%) 70%);
    }
    .decoration-circle-2 {
      background: radial-gradient(circle, rgb(255 165 0 / 20%) 0%, rgb(255 165 0 / 0%) 70%);
    }

    /* 卡片样式 - 黑暗模式 */
    .profile-card {
      background: rgb(30 30 30 / 70%);
      border: 1px solid rgb(255 255 255 / 10%);
      box-shadow: 0 15px 35px rgb(0 0 0 / 30%);
      &:hover {
        background: rgb(40 40 40 / 80%);
        border-color: rgba(var(--el-color-primary-rgb), 0.3);
      }
      .section-title {
        color: var(--el-color-primary-light-3);
        border-bottom: 1px solid rgb(255 255 255 / 10%);
      }
    }

    /* 头像样式 - 黑暗模式 */
    .avatar-wrapper .el-avatar {
      background: rgb(40 40 40 / 50%);
      border-color: rgb(255 255 255 / 20%);
      &:hover {
        border-color: var(--el-color-primary-light-5);
      }
    }
    .user-name {
      color: var(--el-color-primary-light-3);
    }

    /* 信息项样式 - 黑暗模式 */
    .info-item {
      background: rgb(40 40 40 / 50%);
      border: 1px solid rgb(255 255 255 / 10%);
      &:hover {
        background: rgb(50 50 50 / 60%);
        border-color: rgba(var(--el-color-primary-rgb), 0.3);
      }
      .info-label {
        color: var(--el-text-color-secondary);
      }
      .info-value {
        color: var(--el-text-color-primary);
        .el-icon {
          color: var(--el-color-primary-light-3);
        }
        &.empty {
          color: var(--el-text-color-secondary);
        }
      }
      .input-field .el-input__wrapper {
        background: rgb(30 30 30 / 70%);
      }
    }

    /* 链接项样式 - 黑暗模式 */
    .link-item {
      background: rgb(40 40 40 / 50%);
      border: 1px solid rgb(255 255 255 / 10%);
      &:hover {
        background: rgb(50 50 50 / 60%);
        border-color: rgba(var(--el-color-primary-rgb), 0.3);
      }
      .link-icon {
        background: rgba(var(--el-color-primary-rgb), 0.2);
      }
      .link-text {
        color: var(--el-text-color-primary);
      }
      &:hover {
        .link-icon {
          background: rgba(var(--el-color-primary-rgb), 0.3);
        }
        .link-text {
          color: var(--el-color-primary-light-3);
        }
      }
    }

    /* 退出按钮 - 黑暗模式 */
    .logout-button {
      background: linear-gradient(45deg, #ff3838, #ff5252);
      box-shadow: 0 10px 20px rgb(255 82 82 / 40%);
      &:hover {
        background: linear-gradient(45deg, #ff5252, #ff7675);
        box-shadow: 0 15px 30px rgb(255 82 82 / 50%);
      }
    }
  }

  /* 对话框样式 - 黑暗模式 */
  :deep(.el-dialog) {
    background: rgb(30 30 30 / 70%);
    border: 1px solid rgb(255 255 255 / 10%);
    box-shadow: 0 8px 32px rgb(0 0 0 / 30%);
    .el-dialog__header {
      background: rgb(40 40 40 / 50%);
      border-bottom: 1px solid rgb(255 255 255 / 10%);
    }
    .el-dialog__body {
      background: rgb(30 30 30 / 30%);
    }
    .el-dialog__footer {
      background: rgb(40 40 40 / 50%);
      border-top: 1px solid rgb(255 255 255 / 10%);
    }
  }
}

/* 动画关键帧 */
@keyframes float {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
  100% {
    transform: translateY(0) rotate(0deg);
  }
}

@keyframes shine {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes pulse {
  0% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  100% {
    opacity: 0.8;
    transform: scale(1);
  }
}
