<template>
  <ResponsiveWrapper
    :enable-mobile-scroll="true"
    :enable-mobile-actions="true"
    :enable-mobile-search="true"
    :enable-mobile-pagination="true"
    custom-class="main-box"
  >
    <TreeFilter
      ref="treeFilterRef"
      label="name"
      :title="t('common.projectList')"
      :request-api="getUserGroup"
      :default-value="initParam.groupId"
      @change="changeTreeFilter"
    >
      <template #dropdown>
        <el-dropdown-item :icon="CirclePlus" @click="openProjectDrawer(t('project.add'))">
          {{ $t("common.add") }}
        </el-dropdown-item>
        <el-dropdown-item :icon="Delete" @click="confirmDeleteProject">
          {{ $t("common.delete") }}
        </el-dropdown-item>
      </template>
    </TreeFilter>
    <div class="table-box">
      <!-- 标签页头部 -->
      <div class="tabs-header">
        <el-tabs v-model="activeTab" class="tabs-container">
          <!-- 拓扑图标签页 -->
          <el-tab-pane :label="t('topology.title')" name="topology"></el-tab-pane>
          <!-- 项目目录标签页 -->
          <el-tab-pane :label="t('project.title')" name="project"></el-tab-pane>
        </el-tabs>
      </div>

      <!-- 标签页内容 -->
      <div class="tabs-content">
        <!-- 拓扑图内容 -->
        <div v-show="activeTab === 'topology'" class="topology-tab-content">
          <D3Topology
            :data="topologyData"
            :width="chartWidth"
            :height="chartHeight"
            @node-click="handleNodeClick"
            @refresh="handleLoad"
            @set-as-exit-device="handleSetAsExitDeviceClick"
            @cancel-exit-device="handleCancelExitDeviceClick"
            @view-device-details="handleViewDeviceDetails"
          />
        </div>

        <!-- 项目目录内容 -->
        <div v-show="activeTab === 'project'" class="project-tab-content">
          <div class="container" :class="{ collapsed: isCollapsed }">
            <!-- 卡片内容 -->
            <transition name="smooth-collapse">
              <el-space v-if="!isCollapsed" wrap :style="{ gap: '0px' }" class="card-space">
                <el-card
                  v-for="(device, index) in deviceStatistics"
                  :key="index"
                  class="card no-border"
                  shadow="never"
                  @click="handleCardClick(device.deviceType)"
                >
                  <!-- 图片部分 -->
                  <el-row justify="start" class="image-row">
                    <el-col>
                      <img
                        v-if="device.deviceType === 'route'"
                        src="@/assets/images/router_icon.png"
                        class="device-img"
                        alt="router"
                      />
                      <img
                        v-if="device.deviceType === 'switch'"
                        src="@/assets/images/switch_icon.png"
                        class="device-img"
                        alt="switch"
                      />
                      <img
                        v-if="device.deviceType === 'bridge'"
                        src="@/assets/images/bridge_icon.png"
                        class="device-img"
                        alt="bridge"
                      />
                      <img v-if="device.deviceType === 'ap'" src="@/assets/images/ap_icon.png" class="device-img" alt="ap" />
                      <img v-if="device.deviceType === 'ac'" src="@/assets/images/ac_icon.png" class="device-img" alt="ac" />
                      <img
                        v-if="device.deviceType === 'repeater'"
                        src="@/assets/images/zhongji_icon.png"
                        class="device-img"
                        alt="ac"
                      />
                    </el-col>
                  </el-row>

                  <!-- 文字部分 -->
                  <el-row justify="center" class="card-content-row">
                    <span class="card-number">{{ device.count }}</span>
                    <span class="card-label">{{
                      isChinese ? device.deviceTypeName : device.deviceType.charAt(0).toUpperCase() + device.deviceType.slice(1)
                    }}</span>
                  </el-row>
                </el-card>
              </el-space>
            </transition>
          </div>
          <div class="collapse-btn-bar">
            <div class="collapse-divider"></div>
            <button class="collapse-link-btn" @click="toggleCollapse">
              <svg class="arrow-icon" :class="{ up: !isCollapsed, down: isCollapsed }" viewBox="0 0 24 24">
                <path d="M12 16l-6-6h12z" fill="currentColor" />
              </svg>
              <span>{{ isCollapsed ? $t("common.expand") : $t("common.collapse") }}</span>
            </button>
          </div>
          <ProTable
            ref="proTable"
            :key="globalStore.language"
            :columns="columns"
            :request-api="getProjectDeviceListByGroupId"
            :init-param="initParam"
            :search-col="{ xs: 1, sm: 1, md: 2, lg: 3, xl: 3 }"
          >
            <!-- 表格 header 按钮 -->
            <template #tableHeader>
              <el-button type="primary" :icon="CirclePlus" @click="openDrawer(t('device.bind'))" :disabled="!initParam.groupId">{{
                $t("device.bind")
              }}</el-button>
            </template>
            <!-- 表格操作 -->
            <template #operation="scope">
              <el-button type="primary" link :icon="View" @click="openDrawer(t('common.view'), scope.row)">{{
                $t("common.view")
              }}</el-button>
              <!-- <el-button type="primary" link :icon="EditPen" @click="openDrawer(t('common.edit'), scope.row)">{{
                $t("common.edit")
              }}</el-button> -->
              <el-button type="primary" link :icon="Tools" @click="openDeviceConfigDrawer(t('common.manage'), scope.row)">{{
                $t("device.systemSettings")
              }}</el-button>
              <el-button type="primary" link :icon="Delete" @click="unbind(scope.row)">{{ $t("device.unbind") }}</el-button>
            </template>
          </ProTable>
        </div>
      </div>
      <DeviceDrawer v-if="dataReady" ref="drawerRef" />
      <DeviceConfigDrawer v-if="dataReady" ref="drawerDeviceConfigRef" />
      <ProjectDrawer ref="drawerProjectRef" @refresh-tree-filter="updateTreeFilter" />
      <ImportExcel ref="dialogRef" />
    </div>
  </div>
</template>
<script setup lang="ts" name="useTreeFilter">
import { ref, reactive, onMounted, provide, computed, nextTick, onBeforeUnmount, toRaw, watch } from "vue";
import { User } from "@/api/interface";
import type { Project } from "@/api/interface/project";
import { ElMessage, ElMessageBox } from "element-plus";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { CirclePlus, Delete, View, Tools } from "@element-plus/icons-vue";
import { editUser } from "@/api/modules/user";
import {
  getDeviceListByGroupId as getProjectDeviceListByGroupId,
  getDeviceType,
  getUserGroup,
  getDeviceStatistics,
  addProject,
  delProject,
  bindDevice,
  getDeviceModel,
  unbindDevice,
  updateDevice
} from "@/api/modules/project";
import { useUserStore } from "@/stores/modules/user";
import { useI18n } from "vue-i18n";
import { useGlobalStore } from "@/stores/modules/global";
import { useTopology } from "@/hooks/useTopology";
import { useResponsive } from "@/hooks/useResponsive";
import { getDeviceListByGroupId as getTopologyDeviceListByGroupId } from "@/api/modules/topology";
import { processNodeSymbol } from "@/utils/imageProcessor";
import { AxiosCanceler } from "@/api/helper/axiosCancel";
import ProTable from "@/components/ProTable/index.vue";
import TreeFilter from "@/components/TreeFilter/index.vue";
import ImportExcel from "@/components/ImportExcel/index.vue";
import DeviceDrawer from "@/views/project/components/DeviceDrawer.vue";
import ProjectDrawer from "@/views/project/components/ProjectDrawer.vue";
import DeviceConfigDrawer from "@/views/project/components/DeviceConfigDrawer.vue";
import D3Topology from "@/components/D3Topology/index.vue";

const { t, locale } = useI18n();

const globalStore = useGlobalStore();
const isChinese = computed(() => globalStore.language === "zh");

// 响应式布局
const { isMobile, isTablet, responsiveInfo } = useResponsive();

// 标签页相关
const activeTab = ref("topology"); // 默认显示拓扑图标签页

// 响应式容器样式 (暂时移除未使用的变量)
// const containerStyle = computed(() => {
//   const size = getContainerSize();
//   return {
//     padding: size.padding,
//     gap: size.gap,
//     fontSize: size.fontSize
//   };
// });

// 使用拓扑图 hook
const {
  topologyData,
  clickNodeProps,
  generateData,
  loadTopologyData,
  handleSetAsExitDevice,
  handleCancelExitDevice,
  loadExitDevices,
  processExitDevices,
  clearAllDeviceMacAddresses
} = useTopology();

// 图表尺寸
const chartWidth = ref(800);
const chartHeight = ref(600); // 增加高度，使图表更充分

// 拓扑图数据加载状态
const dataLoaded = ref(false);

// const router = useRouter();

// // 跳转详情页
// const toDetail = () => {
//   router.push(`/proTable/useTreeFilter/detail/123456?params=detail-page`);
// };

// ProTable 实例
const proTable = ref<ProTableInstance>();

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ groupId: "" });

// 防抖定时器
let debounceTimer: number | null = null;

// 树形筛选切换
const changeTreeFilter = (val: string | string[], cancelRequests: boolean = true) => {
  // 如果是数组，则忽略这次调用，因为我们只处理单选事件
  if (Array.isArray(val)) {
    return;
  }

  // 清除之前的定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer);
    debounceTimer = null;
  }

  // 确保 groupId 是字符串而不是数组
  initParam.groupId = val;

  // 先清空拓扑数据 - 不能直接给 topologyData 赋值，因为它是一个常量引用
  topologyData.children = [];

  // 重置拓扑图弹窗状态
  clickNodeProps.value.row = {};
  // 关闭设备信息弹窗（通过重置数据实现）

  // 使用防抖机制，避免短时间内发送多次请求
  debounceTimer = window.setTimeout(() => {
    // 当切换树形筛选时，同时刷新拓扑图和项目设备列表
    if (initParam.groupId) {
      // 只有在需要时才取消请求
      if (cancelRequests) {
        // 在加载数据前清除所有未完成的请求
        const axiosCanceler = new AxiosCanceler();
        axiosCanceler.removeAllPending();
      }

      // 同时刷新两个视图
      // 1. 加载拓扑图数据
      handleLoad();

      // 2. 刷新项目设备列表
      proTable.value!.pageable.current = 1;
      proTable.value!.getTableList();

      // 3. 获取设备统计信息
      fetchDeviceStatistics();
    }
  }, 300); // 300毫秒的防抖时间
};

// 存储设备型号的数据
const deviceModels = ref<any[]>([]);
const deviceTypes = ref<any[]>([]);

const isCollapsed = ref(false);
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};

// 查询设备型号并更新状态
const loadDeviceModels = async () => {
  try {
    // 创建一个新的 axios 请求配置，添加不取消标记
    const response = await getDeviceModel();
    if (response && response.data) {
      deviceModels.value = response.data; // 将设备型号数据存储到响应式变量中
    }
  } catch (error) {
    // 如果是取消错误，则忽略
    if (error.name === "CanceledError") {
      console.log("设备型号请求被取消，重新尝试");
      // 重新尝试请求，但不要取消其他请求
      setTimeout(() => loadDeviceModels(), 100);
    } else {
      console.error("加载设备型号失败:", error);
    }
  }
};

// 添加一个计算属性来处理状态枚举
const statusEnum = computed(() => [
  {
    label: isChinese.value ? "离线" : "Offline",
    value: 1,
    tagType: "danger"
  },
  {
    label: isChinese.value ? "在线" : "Online",
    value: 0,
    tagType: "success"
  }
]);

// 表格配置项
const columns = computed<ColumnProps<User.ResUserList>[]>(() => [
  { type: "index", label: "#", width: 80 },
  { prop: "deviceId", label: t("device.deviceId"), width: 160, search: { el: "input" } },
  {
    prop: "deviceModel",
    label: t("device.model"),
    width: 120,
    sortable: true,
    search: { el: "select" },
    enum: deviceModels,
    fieldNames: { label: "deviceModel", value: "deviceModel" }
  },
  { prop: "deviceName", label: t("device.deviceName") },
  {
    prop: "deviceType",
    label: t("device.type"),
    enum: deviceTypes,
    search: { el: "select" },
    fieldNames: { label: isChinese.value ? "configDesc" : "attribute2", value: "configCode" }
  },
  {
    prop: "bootTime",
    label: t("device.bootTime"),
    render: scope => {
      const bootTimeInSeconds = (scope.row as any).bootTime || 0;

      // 转换为年月日时分秒
      const years = Math.floor(bootTimeInSeconds / (365 * 24 * 60 * 60));
      const months = Math.floor((bootTimeInSeconds % (365 * 24 * 60 * 60)) / (30 * 24 * 60 * 60));
      const days = Math.floor((bootTimeInSeconds % (30 * 24 * 60 * 60)) / (24 * 60 * 60));
      const hours = Math.floor((bootTimeInSeconds % (24 * 60 * 60)) / 3600);
      const minutes = Math.floor((bootTimeInSeconds % 3600) / 60);
      const seconds = bootTimeInSeconds % 60;

      // 以数组的形式存储各部分
      const timeParts = [];

      if (years > 0) {
        timeParts.push(`${years}${isChinese.value ? "年" : "yr"}`);
      }
      if (months > 0) {
        timeParts.push(`${months}${isChinese.value ? "月" : "mo"} `);
      }
      if (days > 0) {
        timeParts.push(`${days}${isChinese.value ? "天" : "d"} `);
      }
      if (hours > 0) {
        timeParts.push(`${hours}${isChinese.value ? "时" : "h"} `);
      }
      if (minutes > 0) {
        timeParts.push(`${minutes}${isChinese.value ? "分" : "min"} `);
      }
      if (seconds > 0 || timeParts.length === 0) {
        timeParts.push(`${seconds}${isChinese.value ? "秒" : "s"} `);
      }

      // 拼接时间部分，使用空格分隔
      return timeParts.join("");
    }
  },
  { prop: "mac", label: "MAC", width: 180 },
  { prop: "ipaddr", label: t("common.ipAddress"), width: 180 },
  {
    prop: "status",
    label: t("common.deviceStatus"),
    width: 120,
    sortable: true,
    tag: true,
    enum: statusEnum.value,
    search: { el: "select" }
  },
  {
    prop: "creationTime",
    label: t("common.creationTime"),
    width: 180
  },
  { prop: "operation", label: t("device.operate"), width: 330, fixed: "right" }
]);

// 解绑设备
const unbind = async (row: any) => {
  const params = {
    userId: useUserStore().userInfo.userId,
    deviceId: row.deviceId
  };
  console.log("params:", params);
  new Promise((resolve, reject) => {
    ElMessageBox.confirm(t("device.confirmUnbind"), t("header.warmRemind"), {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
      draggable: true
    })
      .then(async () => {
        const res = await unbindDevice(params);
        if (!res) return reject(false);
        if (res.code === "200") {
          ElMessage({
            type: "success",
            message: t("common.success"),
            duration: 1500,
            showClose: true
          });
        } else {
          ElMessage({
            type: "error",
            message: res.msg,
            duration: 1500,
            showClose: true
          });
        }
        await proTable.value?.getTableList();
        resolve(true);
      })
      .catch(() => {
        // cancel operation
      });
  });
};

// 批量添加用户
const dialogRef = ref<InstanceType<typeof ImportExcel> | null>(null);

// 打开 drawer(新增、查看、编辑)
const drawerRef = ref<InstanceType<typeof DeviceDrawer> | null>(null);
const drawerProjectRef = ref<InstanceType<typeof ProjectDrawer> | null>(null);
const drawerDeviceConfigRef = ref<InstanceType<typeof DeviceConfigDrawer> & { acceptParams: (params: any) => void }>();
const openDrawer = (title: string, row: Partial<Project.ReqProjectParams> = {}) => {
  const params = {
    title,
    isView: title === t("common.view"),
    row: { ...row },
    api: title === t("device.bind") ? bindDevice : title === t("common.edit") ? updateDevice : undefined,
    getTableList: proTable.value?.getTableList,
    projectId: initParam.groupId
  };
  drawerRef.value?.acceptParams(params);
};

const openProjectDrawer = (title: string, row: Partial<Project.ReqProjectParams> = {}) => {
  const params = {
    title,
    isView: title === t("common.view"),
    row: { ...row },
    api: title === t("project.add") ? addProject : title === t("common.edit") ? editUser : undefined,
    getTableList: proTable.value?.getTableList
  };
  drawerProjectRef.value?.acceptParams(params);
};

const openDeviceConfigDrawer = (title: string, row: Partial<Project.ReqProjectParams> = {}) => {
  if ((row as any).status !== undefined && (row as any).status === 1) {
    ElMessage({
      message: t("device.manageOfflineDeviceTip"),
      type: "error",
      plain: true
    });
    return;
  }
  const params = {
    title,
    isView: false,
    row: { ...row },
    getTableList: proTable.value?.getTableList,
    projectId: initParam.groupId
  };
  console.log("params:", params);
  drawerDeviceConfigRef.value?.acceptParams(params);
};

// 响应式变量，用于存储设备统计数据
const deviceStatistics = ref<Project.DeviceStatistic[]>([]);

// 请求设备统计数据
const fetchDeviceStatistics = async () => {
  try {
    // 参数类型可以根据实际需求定义
    const response = await getDeviceStatistics(initParam);

    // 判断响应状态并绑定数据
    if (response.code === "200") {
      console.log("获取设备统计数据成功:", response.data);
      deviceStatistics.value = response.data; // 将转换后的数据赋值给响应式变量
    } else {
      console.error("获取设备统计数据失败:", response.msg);
    }
  } catch (error) {
    console.error("请求出错:", error);
  }
};
// 引入组件实例
const treeFilterRef = ref();
const updateTreeFilter = () => {
  console.log("updateTreeFilter----");
  treeFilterRef.value.refreshData();
  // 触发 `TreeFilter` 的更新逻辑，例如：
  // proTable.value?.getTableList();
  // fetchDeviceStatistics(initParam); // 更新设备统计数据
};

// 删除按钮点击确认
const confirmDeleteProject = () => {
  if (!initParam.groupId) {
    ElMessage.warning(t("project.selectDeleteProject"));
    return;
  }

  ElMessageBox.confirm(t("project.deleteConfirm"), t("project.deleteWarning"), {
    confirmButtonText: t("project.deleteButton"),
    cancelButtonText: t("project.cancelButton"),
    type: "warning"
  })
    .then(() => {
      if (initParam.groupId) {
        delProject(initParam).then(res => {
          if (res.code === "200") {
            treeFilterRef.value.refreshData();
            ElMessage.success(t("project.deleteSuccess"));
          } else {
            ElMessage.error(res.msg);
          }
        });
      } else {
        ElMessage.warning(t("project.selectDeleteProject"));
      }
    })
    .catch(() => {
      ElMessage.info(t("project.deleteCancel"));
    });
};

// 处理卡片点击事件
const handleCardClick = (deviceType: string) => {
  if (proTable.value) {
    proTable.value.searchParam.deviceType = deviceType;
    proTable.value.search();
  }
};

// 在 setup 中调用 provide，提供初始值
provide("deviceTypes", deviceTypes);
const dataReady = ref(false);

const deviceList = ref([]);
const topologyDataResponse = ref([]);

/**
 * 加载拓扑数据并初始化图表
 * 获取设备配置信息，更新拓扑图数据，并渲染图表
 */
const handleLoad = async () => {
  try {
    // 设置加载状态
    dataLoaded.value = false;
    if (!initParam.groupId) {
      // ElMessage.error(t("topology.selectGroup"));
      dataLoaded.value = true;
      return;
    }

    // 重置拓扑图弹窗状态
    clickNodeProps.value.row = {};

    // 清空之前的数据，避免重复显示
    deviceList.value = [];
    topologyDataResponse.value = [];

    // 重置拓扑图数据
    topologyData.children = [];

    // 注意：只在开始加载所有设备拓扑数据前清空一次，而不是每个设备都清空
    clearAllDeviceMacAddresses();

    // 根据分组ID查询设备信息
    const response = await getTopologyDeviceListByGroupId({
      groupId: initParam.groupId
    });

    if (response.code !== "200" || !response.data?.length) {
      // ElMessage.error({
      //   message: t("topology.loadFailed"),
      //   duration: 3000
      // });
      dataLoaded.value = true;
      return;
    }

    // 正确地使用分页数据的 list 属性
    deviceList.value = response.data || [];

    // 处理每个设备的拓扑数据
    for (const device of deviceList.value) {
      // 加载设备的拓扑数据
      const topologyResult = await loadTopologyData(device.deviceId, t);

      if (topologyResult) {
        // 获取父节点信息
        const parentNode = device.parentNode;
        if (parentNode) {
          const parentSport = parentNode.extra?.sport;
          const parentDport = parentNode.extra?.dport;

          // 更新当前设备的sport和dport
          device.extra.sport = parentSport;
          device.extra.dport = parentDport;
        }

        // 更新设备数据中的拓扑信息
        topologyDataResponse.value.push(topologyResult);

        // 生成图表数据
        await generateData(topologyResult, device, t);
      }
    }

    // 加载出口设备信息
    if (initParam.groupId) {
      try {
        const exitDeviceList = await loadExitDevices(initParam.groupId);
        if (exitDeviceList && exitDeviceList.length > 0) {
          // 处理出口设备，将出口设备移动到二级节点位置
          processExitDevices(exitDeviceList);
        }
      } catch (error) {
        console.error("加载出口设备信息失败:", error);
      }
    }

    // 等待 DOM 更新完成
    await nextTick();

    // 设置数据加载完成状态
    dataLoaded.value = true;
  } catch (error) {
    // 错误处理
    console.error("拓扑数据加载失败:", error);
    // ElMessage.error({
    //   message: t("topology.loadFailed"),
    //   duration: 3000
    // });
  } finally {
    // 确保在出错时也将加载状态设为完成
    if (!dataLoaded.value) {
      dataLoaded.value = true;
    }
  }
};

/**
 * 监听拓扑数据变化
 * 当 topologyData.children 数据变化时，自动处理数据
 */
watch(
  () => topologyData.children,
  () => {
    try {
      // 处理图标
      // 克隆拓扑数据以避免修改原始响应式对象
      const processedData = JSON.parse(JSON.stringify(toRaw(topologyData)));

      // 处理所有节点的图标
      processNodeSymbol(processedData).then(() => {
        // D3 组件会自动响应 topologyData 的变化
        console.log("Topology data updated");
      });
    } catch (error) {
      console.error("处理拓扑数据失败:", error);
    }
  },
  { deep: true } // 深度监听数组变化
);

/**
 * 处理图表节点点击事件
 * @param node 点击的节点数据
 */
const handleNodeClick = node => {
  console.log("点击节点:", node);

  // 将点击的节点数据保存到 clickNodeProps
  clickNodeProps.value.row = node;
};

/**
 * 处理"设为出口设备"按钮点击事件
 * @param deviceInfo 设备信息
 */
const handleSetAsExitDeviceClick = async deviceInfo => {
  console.log("点击设为出口设备按钮:", deviceInfo);
  if (!initParam.groupId) {
    ElMessage.error(t("topology.selectGroup"));
    return;
  }

  // 调用 useTopology 中的函数设置出口设备
  const success = await handleSetAsExitDevice(initParam.groupId, deviceInfo, t);

  if (success) {
    // 刷新拓扑图
    handleLoad();
  }
};

/**
 * 处理"取消出口设备"按钮点击事件
 * @param deviceInfo 设备信息
 */
const handleCancelExitDeviceClick = async deviceInfo => {
  console.log("点击取消出口设备按钮:", deviceInfo);
  // 调用 useTopology 中的函数取消出口设备
  const success = await handleCancelExitDevice(deviceInfo, t);

  if (success) {
    // 刷新拓扑图
    handleLoad();
  }
};

/**
 * 处理"查看详情"按钮点击事件
 * @param deviceInfo 设备信息
 */
const handleViewDeviceDetails = deviceInfo => {
  console.log("查看设备详情:", deviceInfo);

  // 1. 切换到项目目录标签页
  activeTab.value = "project";

  // 2. 等待DOM更新
  nextTick(() => {
    // 3. 在表格中查找对应设备
    if (proTable.value) {
      // 清除现有的搜索条件
      proTable.value.searchParam = {};

      // 设置搜索条件为设备ID
      proTable.value.searchParam.deviceId = deviceInfo.deviceId;

      // 执行搜索
      proTable.value.search();

      // 4. 等待表格数据加载完成后打开详情抽屉
      setTimeout(() => {
        // 如果找到了设备，则打开详情抽屉
        if (proTable.value.tableData.length > 0) {
          const deviceData = proTable.value.tableData[0];
          openDrawer(t("common.view"), deviceData);
        } else {
          ElMessage({
            message: t("device.deviceNotFound"),
            type: "warning",
            duration: 2000
          });
        }
      }, 500); // 等待500毫秒，确保表格数据加载完成
    }
  });
};

/**
 * 监听语言变化，刷新拓扑图
 */
watch(locale, () => {
  console.log("Language changed, refreshing topology...");
  // 当语言变化时，重新加载数据
  if (initParam.groupId) {
    handleLoad();
  }
});

// 在组件挂载时加载数据
onMounted(async () => {
  await fetchDeviceStatistics();
  await loadDeviceModels();
  const response = await getDeviceType();
  if (response && response.data) {
    deviceTypes.value = Array.isArray(response.data) ? response.data : [];
    provide("deviceTypes", deviceTypes.value);
    dataReady.value = true; // 数据加载完成后渲染子组件
  }

  // 响应式图表尺寸设置
  const updateChartSize = () => {
    const container = document.querySelector(".topology-tab-content");
    if (container) {
      // 获取容器的宽度
      const containerWidth = container.clientWidth;

      // 获取整个页面的可用高度
      const mainBox = document.querySelector(".main-box");
      const mainBoxHeight = mainBox ? mainBox.clientHeight : window.innerHeight - 120;

      // 获取标签页头部的高度
      const tabsHeader = document.querySelector(".tabs-header");
      const tabsHeaderHeight = tabsHeader ? tabsHeader.clientHeight : 56;

      // 根据设备类型调整边距
      let padding = 20;
      let minHeight = 300;

      if (isMobile.value) {
        padding = 8;
        minHeight = 250;
      } else if (isTablet.value) {
        padding = 12;
        minHeight = 280;
      }

      // 计算拓扑图可用的最大高度
      const availableHeight = mainBoxHeight - tabsHeaderHeight - padding * 2;

      // 计算宽度，确保至少为 100px
      chartWidth.value = Math.max(100, containerWidth - padding);

      // 计算高度，使用可用高度，确保满足最小高度要求
      chartHeight.value = Math.max(minHeight, availableHeight);

      console.log(
        `更新图表尺寸: 宽度=${chartWidth.value}, 高度=${chartHeight.value}, 设备类型=${responsiveInfo.value.breakpoint}`
      );
    }
  };

  // 等待DOM更新后设置图表尺寸
  await nextTick();
  updateChartSize();

  // 再次延迟调用，确保在所有内容加载完成后正确计算尺寸
  setTimeout(() => {
    updateChartSize();
  }, 300);

  // 监听窗口大小变化
  window.addEventListener("resize", updateChartSize);

  // 监听标签页切换，当切换到拓扑图标签页时更新尺寸
  watch(activeTab, newVal => {
    if (newVal === "topology") {
      // 当切换到拓扑图标签页时，等待DOM更新后更新图表尺寸
      nextTick(() => {
        updateChartSize();
      });
    }
  });

  // 组件卸载时清理
  onBeforeUnmount(() => {
    window.removeEventListener("resize", updateChartSize);
  });

  // 获取树形菜单数据并选中第一个节点
  if (treeFilterRef.value) {
    // 如果树形菜单实例存在，获取数据
    const { data } = await getUserGroup();
    if (data && data.length > 0) {
      // 选中第一个节点
      const firstNode = data[0];
      if (firstNode && firstNode.id) {
        // 设置当前选中节点
        if (treeFilterRef.value.treeRef && typeof treeFilterRef.value.treeRef.setCurrentKey === "function") {
          treeFilterRef.value.treeRef.setCurrentKey(firstNode.id);
        }
        // 触发数据加载，但不取消请求
        changeTreeFilter(firstNode.id, false);
      }
    }
  }
});
</script>
<style scoped lang="scss">
@import "./index";
</style>
