.filter {
  box-sizing: border-box;
  flex-shrink: 0;
  width: 220px;
  min-width: 220px;
  min-height: 160px;
  padding: 18px;
  font-size: 18px;
  font-weight: bold;
  color: var(--el-color-info-dark-2);
  letter-spacing: 0.5px;
  background: var(--el-bg-color);
  border-radius: 16px;
  box-shadow: 0 8px 24px rgb(0 0 0 / 5%);
  transition: all 0.3s ease;
  &:hover {
    box-shadow: 0 12px 32px rgb(0 0 0 / 8%);
    transform: translateY(-2px);
  }
  .title {
    position: relative;
    padding-left: 12px;
    margin: 0 0 15px;
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    letter-spacing: 0.5px;
    &::before {
      position: absolute;
      top: 50%;
      left: 0;
      width: 4px;
      height: 16px;
      content: "";
      background: var(--el-color-primary);
      border-radius: 2px;
      transform: translateY(-50%);
    }
  }
}
.online-text {
  color: green;
}
.offline-text {
  color: red;
}
.legend {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
.legend-item {
  display: flex;
  align-items: center;
  margin: 0 10px;
}
.legend-line {
  display: inline-block;
  width: 30px;
  height: 3px;
  margin-right: 5px;
}
.legend-line.success {
  background-color: #00c6ff;
}
.legend-line.process {
  background-color: #ffbf00;
}
.legend-line.failed {
  background-color: #ff0000;
}
.card-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 设备列表标题样式 */
.device-list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  padding: 0 16px;
  margin-bottom: 10px;
  background: var(--el-bg-color-overlay);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
  .title-section {
    display: flex;
    align-items: center;
    .font-bold {
      position: relative;
      padding-left: 12px;
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      letter-spacing: 0.5px;
      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: 4px;
        height: 16px;
        content: "";
        background: var(--el-color-primary);
        border-radius: 2px;
        transform: translateY(-50%);
      }
    }
  }
}

/* 设备列表容器样式 */
.device-list-container {
  flex: 1;
  padding: 0 4px;
  overflow-y: auto;
  scrollbar-width: thin;
  &::-webkit-scrollbar {
    width: 6px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color-darker);
    border-radius: 20px;
  }
  .box-card {
    margin-bottom: 12px;
    transition: all 0.3s ease;
    &:hover {
      box-shadow: 0 6px 16px rgb(0 0 0 / 8%);
      transform: translateY(-2px);
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
}
.device-img {
  width: 40px;
  height: 40px;
  margin-bottom: 10px;
  object-fit: contain; /* 确保图片比例适配 */
}
.device-card {
  margin-bottom: 16px;
  background: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 12px;
  transition: all 0.3s ease;
  &:hover {
    border-color: var(--el-color-primary-light-7);
    box-shadow: 0 4px 12px rgb(0 0 0 / 5%);
    transform: translateY(-2px);
  }
  .card-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    margin-bottom: 24px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    strong {
      font-size: 15px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
    .el-icon {
      padding: 8px;
      color: var(--el-text-color-secondary);
      border-radius: 6px;
      transition: all 0.3s ease;
      &:hover {
        color: var(--el-color-primary);
        background: var(--el-color-primary-light-9);
        transform: rotate(90deg);
      }
    }
  }
  .el-row {
    display: flex;
    align-items: center;
    padding: 20px 24px;
    margin-bottom: 32px;
    border-radius: 8px;
    transition: all 0.3s ease;
    &:hover {
      background: var(--el-color-primary-light-9);
    }
    &:last-child {
      margin-bottom: 0;
    }
    .el-col {
      width: 100%;
    }
    .el-text {
      display: flex;
      align-items: center;
      font-size: 14px;
      line-height: 1.8;
      color: var(--el-text-color-regular);
      strong {
        position: relative;
        padding-left: 12px;
        margin-right: 8px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        &::before {
          position: absolute;
          top: 50%;
          left: 0;
          width: 2px;
          height: 14px;
          content: "";
          background: var(--el-color-primary);
          border-radius: 1px;
          transform: translateY(-50%);
        }
      }
    }
  }
  .online-text {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    color: var(--el-color-success);
    &::before {
      display: inline-block;
      width: 6px;
      height: 6px;
      margin-right: 8px;
      content: "";
      background: var(--el-color-success);
      border-radius: 50%;
    }
  }
  .offline-text {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    color: var(--el-color-danger);
    &::before {
      display: inline-block;
      width: 6px;
      height: 6px;
      margin-right: 8px;
      content: "";
      background: var(--el-color-danger);
      border-radius: 50%;
    }
  }
}
.device-mac {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 5px;
  font-size: 12px;
  color: #999999;
}
.selected-card {
  box-shadow: 0 4px 8px rgb(0 0 0 / 20%);
}
.custom-dialog .dialog-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.custom-dialog .dialog-body {
  flex: 1;
  min-height: 160px; /* 防止内容溢出 */
  overflow: auto;
}
.custom-dialog .dialog-footer {
  padding: 10px;
  text-align: right;

  //background-color: #ffffff; /* 如有需要可设置背景色 */
  border-top: 1px solid #ebeef5;
}
.main-box {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  padding: 20px;
  background-color: var(--el-bg-color);
  border-radius: 16px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
  transition: all 0.3s ease;
  &:hover {
    box-shadow: 0 4px 16px 0 rgb(0 0 0 / 10%);
  }
  .main-content {
    flex: 1;
    overflow: auto;
    .box-card {
      min-height: 160px;
      background: var(--el-bg-color);
      border-radius: 16px;
      box-shadow: 0 8px 24px rgb(0 0 0 / 5%);
      transition: all 0.3s ease;
      &:hover {
        box-shadow: 0 12px 32px rgb(0 0 0 / 8%);
        transform: translateY(-2px);
      }
      .el-card__header {
        padding: 20px 24px;
        border-bottom: 1px solid var(--el-border-color-lighter);
        .clearfix {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;

          // 通用按钮样式
          span {
            position: relative;
            padding-left: 12px;
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            &::before {
              position: absolute;
              top: 50%;
              left: 0;
              width: 4px;
              height: 16px;
              content: "";
              background: var(--el-color-primary);
              border-radius: 2px;
              transform: translateY(-50%);
            }
          }
          .el-button {
            padding: 8px 16px;
            margin-left: auto;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.3s ease;
            &:hover {
              box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
              transform: translateY(-1px);
            }
            &.action-button {
              position: relative;
              padding: 10px 20px;
              overflow: hidden;
              font-size: 15px;
              font-weight: 600;
              letter-spacing: 0.5px;
              border-radius: 10px;
              box-shadow: 0 6px 16px rgba(var(--el-color-primary-rgb), 0.3);
              &::before {
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                content: "";
                background: linear-gradient(
                  90deg,
                  rgb(255 255 255 / 0%) 0%,
                  rgb(255 255 255 / 20%) 50%,
                  rgb(255 255 255 / 0%) 100%
                );
                transition: all 0.8s ease;
              }
              &:hover {
                box-shadow: 0 8px 20px rgba(var(--el-color-primary-rgb), 0.5);
                transform: translateY(-3px);
                &::before {
                  left: 100%;
                }
              }
              .el-icon {
                margin-right: 6px;
                font-size: 16px;
                transition: all 0.3s ease;
              }
              &:hover .el-icon {
                transform: scale(1.2);
              }

              // 暗色模式适配
              .dark & {
                box-shadow: 0 6px 16px rgba(var(--el-color-primary-rgb), 0.2);
                &::before {
                  background: linear-gradient(90deg, rgb(0 0 0 / 0%) 0%, rgb(255 255 255 / 10%) 50%, rgb(0 0 0 / 0%) 100%);
                }
                &:hover {
                  box-shadow: 0 8px 20px rgba(var(--el-color-primary-rgb), 0.3);
                }
              }
            }
            &.refresh-button {
              margin-right: 10px;
            }
            &.add-button {
              // 添加按钮特有样式
            }
          }
        }
      }
      .el-card__body {
        padding: 24px;
        .el-container {
          .el-aside {
            overflow: hidden;
            .card {
              &.content-box {
                height: calc(100% - 48px); /* 减去两侧的padding值 */
                padding: 24px;
                background: var(--el-bg-color-overlay);
                border-radius: 16px;
                box-shadow: 0 4px 12px rgb(0 0 0 / 3%);
                transition: all 0.3s ease;
                &:hover {
                  box-shadow: 0 6px 16px rgb(0 0 0 / 5%);
                }
              }
            }
          }
          .el-main {
            padding: 0 20px;
            .card {
              margin-bottom: 20px;
              .clearfix {
                display: flex;
                align-items: center;
                justify-content: space-between;
                span {
                  position: relative;
                  padding-left: 12px;
                  font-size: 15px;
                  font-weight: 600;
                  color: var(--el-text-color-primary);
                  &::before {
                    position: absolute;
                    top: 50%;
                    left: 0;
                    width: 4px;
                    height: 16px;
                    content: "";
                    background: var(--el-color-primary);
                    border-radius: 2px;
                    transform: translateY(-50%);
                  }
                }
              }
            }
          }
        }
      }
    }

    // 图例样式
    .legend {
      display: flex;
      gap: 20px;
      justify-content: center;
      padding: 12px 16px;
      margin-top: 20px;
      background: var(--el-bg-color-overlay);
      border-radius: 8px;
      .legend-item {
        display: flex;
        gap: 8px;
        align-items: center;
        padding: 4px 12px;
        border-radius: 16px;
        transition: all 0.3s ease;
        &:hover {
          background: var(--el-color-primary-light-9);
        }
        .legend-line {
          width: 30px;
          height: 3px;
          border-radius: 2px;
        }
        span {
          font-size: 14px;
          font-weight: 500;
          color: var(--el-text-color-regular);
        }
      }
    }
  }
}
