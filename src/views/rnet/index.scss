.filter {
  box-sizing: border-box;
  flex-shrink: 0;
  width: 220px;
  min-width: 220px;
  min-height: 160px;
  padding: 18px;
  font-size: 18px;
  font-weight: bold;
  color: var(--el-color-info-dark-2);
  letter-spacing: 0.5px;
  background: var(--el-bg-color);
  border-radius: 16px;
  box-shadow: 0 8px 24px rgb(0 0 0 / 5%);
  transition: all 0.3s ease;
  &:hover {
    box-shadow: 0 12px 32px rgb(0 0 0 / 8%);
    transform: translateY(-2px);
  }
  .title {
    position: relative;
    padding-left: 12px;
    margin: 0 0 15px;
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    letter-spacing: 0.5px;
    &::before {
      position: absolute;
      top: 50%;
      left: 0;
      width: 4px;
      height: 16px;
      content: "";
      background: var(--el-color-primary);
      border-radius: 2px;
      transform: translateY(-50%);
    }
  }
}
.online-text {
  color: green;
}
.offline-text {
  color: red;
}
.legend {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
.legend-item {
  display: flex;
  align-items: center;
  margin: 0 10px;
}
.legend-line {
  display: inline-block;
  width: 30px;
  height: 3px;
  margin-right: 5px;
}
.legend-line.success {
  background-color: #00c6ff;
}
.legend-line.process {
  background-color: #ffbf00;
}
.legend-line.failed {
  background-color: #ff0000;
}
.card-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 设备列表标题样式 */
.device-list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  padding: 0 16px;
  margin-bottom: 10px;
  background: var(--el-bg-color-overlay);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
  .title-section {
    display: flex;
    align-items: center;
    .font-bold {
      position: relative;
      padding-left: 12px;
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      letter-spacing: 0.5px;
      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: 4px;
        height: 16px;
        content: "";
        background: var(--el-color-primary);
        border-radius: 2px;
        transform: translateY(-50%);
      }
    }
  }
}

/* 设备列表容器样式 */
.device-list-container {
  flex: 1;
  padding: 0 4px;
  overflow-y: auto;
  scrollbar-width: thin;
  &::-webkit-scrollbar {
    width: 6px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color-darker);
    border-radius: 20px;
  }
  .box-card {
    margin-bottom: 12px;
    transition: all 0.3s ease;
    &:hover {
      box-shadow: 0 6px 16px rgb(0 0 0 / 8%);
      transform: translateY(-2px);
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
}
.device-img {
  width: 40px;
  height: 40px;
  margin-bottom: 10px;
  object-fit: contain; /* 确保图片比例适配 */
}
.device-card {
  margin-bottom: 16px;
  background: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 12px;
  transition: all 0.3s ease;
  &:hover {
    border-color: var(--el-color-primary-light-7);
    box-shadow: 0 4px 12px rgb(0 0 0 / 5%);
    transform: translateY(-2px);
  }
  .card-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    margin-bottom: 24px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    strong {
      font-size: 15px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
    .el-icon {
      padding: 8px;
      color: var(--el-text-color-secondary);
      border-radius: 6px;
      transition: all 0.3s ease;
      &:hover {
        color: var(--el-color-primary);
        background: var(--el-color-primary-light-9);
        transform: rotate(90deg);
      }
    }
  }
  .el-row {
    display: flex;
    align-items: center;
    padding: 20px 24px;
    margin-bottom: 32px;
    border-radius: 8px;
    transition: all 0.3s ease;
    &:hover {
      background: var(--el-color-primary-light-9);
    }
    &:last-child {
      margin-bottom: 0;
    }
    .el-col {
      width: 100%;
    }
    .el-text {
      display: flex;
      align-items: center;
      font-size: 14px;
      line-height: 1.8;
      color: var(--el-text-color-regular);
      strong {
        position: relative;
        padding-left: 12px;
        margin-right: 8px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        &::before {
          position: absolute;
          top: 50%;
          left: 0;
          width: 2px;
          height: 14px;
          content: "";
          background: var(--el-color-primary);
          border-radius: 1px;
          transform: translateY(-50%);
        }
      }
    }
  }
  .online-text {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    color: var(--el-color-success);
    &::before {
      display: inline-block;
      width: 6px;
      height: 6px;
      margin-right: 8px;
      content: "";
      background: var(--el-color-success);
      border-radius: 50%;
    }
  }
  .offline-text {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    color: var(--el-color-danger);
    &::before {
      display: inline-block;
      width: 6px;
      height: 6px;
      margin-right: 8px;
      content: "";
      background: var(--el-color-danger);
      border-radius: 50%;
    }
  }
}
.device-mac {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 5px;
  font-size: 12px;
  color: #999999;
}
.selected-card {
  box-shadow: 0 4px 8px rgb(0 0 0 / 20%);
}
.custom-dialog .dialog-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.custom-dialog .dialog-body {
  flex: 1;
  min-height: 160px; /* 防止内容溢出 */
  overflow: auto;
}
.custom-dialog .dialog-footer {
  padding: 10px;
  text-align: right;

  //background-color: #ffffff; /* 如有需要可设置背景色 */
  border-top: 1px solid #ebeef5;
}
/* ===== 异地组网页面响应式布局 ===== */

/* 基础样式 - 确保所有元素可见 */
.main-box.rnet-page {
  display: flex !important;
  width: 100% !important;
  min-height: 400px !important;

  /* 确保TreeFilter始终可见 */
  .tree-filter,
  .filter {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 1 !important;
  }

  /* 确保主内容区域始终可见 */
  .main-content {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 1 !important;
  }
}

/* 桌面端布局 - 最小化干预，保持原始样式 */
@media screen and (min-width: 768px) {
  .main-box.rnet-page {
    display: flex;
    flex-direction: row;
    gap: 16px;
    align-items: flex-start;

    /* TreeFilter 左侧 - 完全保持原始样式 */
    .tree-filter,
    .filter {
      order: 1;
      margin-right: 0;
      margin-bottom: 0;
    }

    /* 主内容区域右侧 */
    .main-content {
      flex: 1;
      order: 2;
      min-width: 0;

      .box-card {
        .el-card__body {
          .el-container {
            /* 网络拓扑图区域 */
            .el-aside {
              flex: 1;
              margin-right: 16px;
            }

            /* 设备列表区域 - 只设置宽度，保持其他原始样式 */
            .el-main {
              flex: 0 0 320px;
              width: 320px;
            }
          }
        }
      }
    }
  }
}

/* 移动端布局 - 垂直布局，保持原始样式 */
@media screen and (max-width: 767px) {
  .main-box.rnet-page {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 12px;

    /* 主内容区域在上方 */
    .main-content {
      flex: 1;
      order: 1;
      min-height: 400px;

      /* 移动端卡片布局 */
      .box-card {
        .el-card__body {
          .el-container {
            flex-direction: column;

            .el-aside {
              width: 100%;
              height: 250px;
              margin: 0 0 12px 0;
            }

            .el-main {
              flex: 1;
              height: 200px;
            }
          }
        }
      }
    }

    /* TreeFilter 在下方 */
    .tree-filter,
    .filter {
      order: 2;
      height: 180px;
      overflow-y: auto;
    }
  }
}









