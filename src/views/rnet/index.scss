.filter {
  box-sizing: border-box;
  flex-shrink: 0;
  width: 220px;
  min-width: 220px;
  min-height: 160px;
  padding: 18px;
  font-size: 18px;
  font-weight: bold;
  color: var(--el-color-info-dark-2);
  letter-spacing: 0.5px;
  background: var(--el-bg-color);
  border-radius: 16px;
  box-shadow: 0 8px 24px rgb(0 0 0 / 5%);
  transition: all 0.3s ease;
  &:hover {
    box-shadow: 0 12px 32px rgb(0 0 0 / 8%);
    transform: translateY(-2px);
  }
  .title {
    position: relative;
    padding-left: 12px;
    margin: 0 0 15px;
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    letter-spacing: 0.5px;
    &::before {
      position: absolute;
      top: 50%;
      left: 0;
      width: 4px;
      height: 16px;
      content: "";
      background: var(--el-color-primary);
      border-radius: 2px;
      transform: translateY(-50%);
    }
  }
}
.online-text {
  color: green;
}
.offline-text {
  color: red;
}
.legend {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
.legend-item {
  display: flex;
  align-items: center;
  margin: 0 10px;
}
.legend-line {
  display: inline-block;
  width: 30px;
  height: 3px;
  margin-right: 5px;
}
.legend-line.success {
  background-color: #00c6ff;
}
.legend-line.process {
  background-color: #ffbf00;
}
.legend-line.failed {
  background-color: #ff0000;
}
.card-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 设备列表标题样式 */
.device-list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  padding: 0 16px;
  margin-bottom: 10px;
  background: var(--el-bg-color-overlay);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
  .title-section {
    display: flex;
    align-items: center;
    .font-bold {
      position: relative;
      padding-left: 12px;
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      letter-spacing: 0.5px;
      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: 4px;
        height: 16px;
        content: "";
        background: var(--el-color-primary);
        border-radius: 2px;
        transform: translateY(-50%);
      }
    }
  }
}

/* 设备列表容器样式 */
.device-list-container {
  flex: 1;
  padding: 0 4px;
  overflow-y: auto;
  scrollbar-width: thin;
  &::-webkit-scrollbar {
    width: 6px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color-darker);
    border-radius: 20px;
  }
  .box-card {
    margin-bottom: 12px;
    transition: all 0.3s ease;
    &:hover {
      box-shadow: 0 6px 16px rgb(0 0 0 / 8%);
      transform: translateY(-2px);
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
}
.device-img {
  width: 40px;
  height: 40px;
  margin-bottom: 10px;
  object-fit: contain; /* 确保图片比例适配 */
}
.device-card {
  margin-bottom: 16px;
  background: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 12px;
  transition: all 0.3s ease;
  &:hover {
    border-color: var(--el-color-primary-light-7);
    box-shadow: 0 4px 12px rgb(0 0 0 / 5%);
    transform: translateY(-2px);
  }
  .card-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    margin-bottom: 24px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    strong {
      font-size: 15px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
    .el-icon {
      padding: 8px;
      color: var(--el-text-color-secondary);
      border-radius: 6px;
      transition: all 0.3s ease;
      &:hover {
        color: var(--el-color-primary);
        background: var(--el-color-primary-light-9);
        transform: rotate(90deg);
      }
    }
  }
  .el-row {
    display: flex;
    align-items: center;
    padding: 20px 24px;
    margin-bottom: 32px;
    border-radius: 8px;
    transition: all 0.3s ease;
    &:hover {
      background: var(--el-color-primary-light-9);
    }
    &:last-child {
      margin-bottom: 0;
    }
    .el-col {
      width: 100%;
    }
    .el-text {
      display: flex;
      align-items: center;
      font-size: 14px;
      line-height: 1.8;
      color: var(--el-text-color-regular);
      strong {
        position: relative;
        padding-left: 12px;
        margin-right: 8px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        &::before {
          position: absolute;
          top: 50%;
          left: 0;
          width: 2px;
          height: 14px;
          content: "";
          background: var(--el-color-primary);
          border-radius: 1px;
          transform: translateY(-50%);
        }
      }
    }
  }
  .online-text {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    color: var(--el-color-success);
    &::before {
      display: inline-block;
      width: 6px;
      height: 6px;
      margin-right: 8px;
      content: "";
      background: var(--el-color-success);
      border-radius: 50%;
    }
  }
  .offline-text {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    color: var(--el-color-danger);
    &::before {
      display: inline-block;
      width: 6px;
      height: 6px;
      margin-right: 8px;
      content: "";
      background: var(--el-color-danger);
      border-radius: 50%;
    }
  }
}
.device-mac {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 5px;
  font-size: 12px;
  color: #999999;
}
.selected-card {
  box-shadow: 0 4px 8px rgb(0 0 0 / 20%);
}
.custom-dialog .dialog-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.custom-dialog .dialog-body {
  flex: 1;
  min-height: 160px; /* 防止内容溢出 */
  overflow: auto;
}
.custom-dialog .dialog-footer {
  padding: 10px;
  text-align: right;

  //background-color: #ffffff; /* 如有需要可设置背景色 */
  border-top: 1px solid #ebeef5;
}
/* ===== 异地组网页面响应式布局 ===== */

/* 基础样式 - 确保所有元素可见 */
.main-box.rnet-page {
  display: flex !important;
  width: 100% !important;
  min-height: 400px !important;

  /* 确保TreeFilter始终可见 */
  .tree-filter,
  .filter {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 1 !important;
  }

  /* 确保主内容区域始终可见 */
  .main-content {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 1 !important;
  }
}

/* 桌面端布局 - 简单正确的布局 */
@media screen and (min-width: 768px) {
  .main-box.rnet-page {
    display: flex !important;
    flex-direction: row !important;
    gap: 20px !important;
    align-items: flex-start !important;
    padding: 20px !important;

    /* TreeFilter 左侧 */
    .tree-filter,
    .filter {
      flex: 0 0 240px !important;
      width: 240px !important;
      order: 1 !important;
      margin-right: 0 !important;
      margin-bottom: 0 !important;
      display: block !important;
      visibility: visible !important;
    }

    /* 主内容区域右侧 */
    .main-content {
      flex: 1 !important;
      order: 2 !important;
      min-width: 0 !important;

      .box-card {
        .el-card__body {
          .el-container {
            .el-aside {
              flex: 1 !important;
              margin-right: 20px !important;
            }

            .el-main {
              flex: 0 0 280px !important;
              width: 280px !important;
              overflow-y: auto !important;
              max-height: 600px !important;

              /* 滚动条样式 */
              &::-webkit-scrollbar {
                width: 6px !important;
              }

              &::-webkit-scrollbar-track {
                background: var(--el-fill-color-lighter) !important;
                border-radius: 3px !important;
              }

              &::-webkit-scrollbar-thumb {
                background: var(--el-border-color) !important;
                border-radius: 3px !important;

                &:hover {
                  background: var(--el-border-color-dark) !important;
                }
              }
            }
          }
        }
      }
    }
  }
}

/* 移动端布局 - 垂直布局，主内容在上 */
@media screen and (max-width: 767px) {
  .main-box.rnet-page {
    display: flex !important;
    flex-direction: column !important;
    gap: 12px !important;
    align-items: stretch !important;
    padding: 12px !important;
    margin: 0 !important;
    min-height: calc(100vh - 120px) !important;
    width: 100% !important;

    /* 主内容区域在上方 */
    .main-content {
      flex: 1 !important;
      order: 1 !important;
      min-height: 400px !important;
      margin-bottom: 0 !important;

      /* 移动端卡片布局 */
      .box-card {
        .el-card__body {
          .el-container {
            flex-direction: column !important;

            .el-aside {
              width: 100% !important;
              height: 250px !important;
              margin: 0 0 12px 0 !important;
            }

            .el-main {
              flex: 1 !important;
              height: 200px !important;
              overflow-y: auto !important;

              /* 移动端滚动条 */
              &::-webkit-scrollbar {
                width: 4px !important;
              }

              &::-webkit-scrollbar-thumb {
                background: var(--el-border-color-light) !important;
                border-radius: 2px !important;
              }
            }
          }
        }
      }
    }

    /* TreeFilter 在下方 */
    .tree-filter,
    .filter {
      flex: 0 0 auto !important;
      order: 2 !important;
      height: 180px !important;
      overflow-y: auto !important;
      margin-top: 0 !important;
      display: block !important;
      visibility: visible !important;

      /* TreeFilter 移动端样式 */
      .title {
        font-size: 14px !important;
        margin-bottom: 8px !important;
      }

      .search {
        margin-bottom: 8px !important;
      }

      .el-tree {
        font-size: 13px !important;

        .el-tree-node__content {
          height: 28px !important;
        }
      }
    }

      /* 树形菜单在移动端的优化 */
      .el-tree {
        background-color: transparent !important;

        .el-tree-node {
          .el-tree-node__content {
            padding: 8px 12px !important;
            font-size: 14px !important;
          }
        }
      }
    }
  }

/* 小屏幕进一步优化 */
@media screen and (max-width: 575px) {
  .main-box.rnet-page {
    gap: 12px !important;
    padding: 12px !important;
    border-radius: 8px !important;

    .main-content {
      min-height: 300px !important;
      padding: 12px !important;
      border-radius: 6px !important;
    }

    .tree-filter {
      .filter {
        padding: 8px !important;
      }

      .el-tree {
        .el-tree-node {
          .el-tree-node__content {
            padding: 6px 8px !important;
            font-size: 13px !important;
          }
        }
      }
    }
  }
}

/* 平板端优化 */
@media screen and (min-width: 768px) and (max-width: 1199px) {
  .main-box.rnet-page {
    gap: 14px !important;
    padding: 14px !important;

    .tree-filter,
    .filter {
      width: 220px !important;
      min-width: 220px !important;
      max-width: 220px !important;
      flex: 0 0 220px !important;
      padding: 14px !important;
    }

    .main-content {
      .box-card {
        .el-card__header {
          padding: 14px 18px !important;
        }

        .el-card__body {
          padding: 14px !important;
        }
      }
    }
  }
}

/* 大屏幕优化 */
@media screen and (min-width: 1200px) {
  .main-box.rnet-page {
    gap: 20px !important;
    padding: 20px !important;

    .tree-filter,
    .filter {
      width: 260px !important;
      min-width: 260px !important;
      max-width: 260px !important;
      flex: 0 0 260px !important;
      padding: 20px !important;
    }

    .main-content {
      .box-card {
        .el-card__header {
          padding: 18px 24px !important;
        }

        .el-card__body {
          padding: 20px !important;
        }
      }
    }
  }
}

/* ===== 主内容区域样式 ===== */
  .main-content {
    flex: 1 !important;
    min-width: 0 !important;
    overflow: visible !important;
    .box-card {
      min-height: 160px;
      background: var(--el-bg-color);
      border-radius: 16px;
      box-shadow: 0 8px 24px rgb(0 0 0 / 5%);
      transition: all 0.3s ease;
      &:hover {
        box-shadow: 0 12px 32px rgb(0 0 0 / 8%);
        transform: translateY(-2px);
      }
      .el-card__header {
        padding: 20px 24px;
        border-bottom: 1px solid var(--el-border-color-lighter);
        .clearfix {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;

          // 通用按钮样式
          span {
            position: relative;
            padding-left: 12px;
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            &::before {
              position: absolute;
              top: 50%;
              left: 0;
              width: 4px;
              height: 16px;
              content: "";
              background: var(--el-color-primary);
              border-radius: 2px;
              transform: translateY(-50%);
            }
          }
          .el-button {
            padding: 8px 16px;
            margin-left: auto;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.3s ease;
            &:hover {
              box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
              transform: translateY(-1px);
            }
            &.action-button {
              position: relative;
              padding: 10px 20px;
              overflow: hidden;
              font-size: 15px;
              font-weight: 600;
              letter-spacing: 0.5px;
              border-radius: 10px;
              box-shadow: 0 6px 16px rgba(var(--el-color-primary-rgb), 0.3);
              &::before {
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                content: "";
                background: linear-gradient(
                  90deg,
                  rgb(255 255 255 / 0%) 0%,
                  rgb(255 255 255 / 20%) 50%,
                  rgb(255 255 255 / 0%) 100%
                );
                transition: all 0.8s ease;
              }
              &:hover {
                box-shadow: 0 8px 20px rgba(var(--el-color-primary-rgb), 0.5);
                transform: translateY(-3px);
                &::before {
                  left: 100%;
                }
              }
              .el-icon {
                margin-right: 6px;
                font-size: 16px;
                transition: all 0.3s ease;
              }
              &:hover .el-icon {
                transform: scale(1.2);
              }

              // 暗色模式适配
              .dark & {
                box-shadow: 0 6px 16px rgba(var(--el-color-primary-rgb), 0.2);
                &::before {
                  background: linear-gradient(90deg, rgb(0 0 0 / 0%) 0%, rgb(255 255 255 / 10%) 50%, rgb(0 0 0 / 0%) 100%);
                }
                &:hover {
                  box-shadow: 0 8px 20px rgba(var(--el-color-primary-rgb), 0.3);
                }
              }
            }
            &.refresh-button {
              margin-right: 10px;
            }
            &.add-button {
              // 添加按钮特有样式
            }
          }
        }
      }
      .el-card__body {
        padding: 24px;
        .el-container {
          .el-aside {
            overflow: hidden;
            .card {
              &.content-box {
                height: calc(100% - 48px); /* 减去两侧的padding值 */
                padding: 24px;
                background: var(--el-bg-color-overlay);
                border-radius: 16px;
                box-shadow: 0 4px 12px rgb(0 0 0 / 3%);
                transition: all 0.3s ease;
                &:hover {
                  box-shadow: 0 6px 16px rgb(0 0 0 / 5%);
                }
              }
            }
          }
          .el-main {
            padding: 0 20px;
            .card {
              margin-bottom: 20px;
              .clearfix {
                display: flex;
                align-items: center;
                justify-content: space-between;
                span {
                  position: relative;
                  padding-left: 12px;
                  font-size: 15px;
                  font-weight: 600;
                  color: var(--el-text-color-primary);
                  &::before {
                    position: absolute;
                    top: 50%;
                    left: 0;
                    width: 4px;
                    height: 16px;
                    content: "";
                    background: var(--el-color-primary);
                    border-radius: 2px;
                    transform: translateY(-50%);
                  }
                }
              }
            }
          }
        }
      }
    }

    // 图例样式
    .legend {
      display: flex;
      gap: 20px;
      justify-content: center;
      padding: 12px 16px;
      margin-top: 20px;
      background: var(--el-bg-color-overlay);
      border-radius: 8px;
      .legend-item {
        display: flex;
        gap: 8px;
        align-items: center;
        padding: 4px 12px;
        border-radius: 16px;
        transition: all 0.3s ease;
        &:hover {
          background: var(--el-color-primary-light-9);
        }
        .legend-line {
          width: 30px;
          height: 3px;
          border-radius: 2px;
        }
        span {
          font-size: 14px;
          font-weight: 500;
          color: var(--el-text-color-regular);
        }
      }
    }
  }

