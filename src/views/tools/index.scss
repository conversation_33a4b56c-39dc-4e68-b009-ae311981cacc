/* 亮色模式样式 */
.tools-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden; /* 禁止滚动条 */
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

  /* 添加装饰元素 */
  &::before {
    position: absolute;
    top: -50px;
    right: -50px;
    z-index: 0;
    width: 300px;
    height: 300px;
    content: "";
    background: radial-gradient(circle, rgb(255 255 255 / 80%) 0%, rgb(255 255 255 / 0%) 70%);
    border-radius: 50%;
    opacity: 0.4;
  }
  &::after {
    position: absolute;
    bottom: -100px;
    left: -100px;
    z-index: 0;
    width: 400px;
    height: 400px;
    content: "";
    background: radial-gradient(circle, rgb(173 216 230 / 80%) 0%, rgb(173 216 230 / 0%) 70%);
    border-radius: 50%;
    opacity: 0.3;
  }
}
.table-box {
  position: relative;
  z-index: 1; /* 确保在装饰元素之上 */
  box-sizing: border-box;
  display: grid;
  grid-template-columns: repeat(4, minmax(220px, 220px));
  gap: 20px;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 20px;
  margin: 0;
  .avatar-div {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    aspect-ratio: 1;
    padding: 10px;
    cursor: pointer;

    /* 毕玻璃效果 */
    background: rgb(255 255 255 / 10%);
    backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgb(255 255 255 / 18%);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgb(0 0 0 / 10%);
    transition: all 0.3s ease;
    &:hover {
      background: rgb(255 255 255 / 20%);
      box-shadow: 0 12px 32px rgb(0 0 0 / 15%);
      transform: translateY(-5px);
      .el-avatar {
        border-color: var(--el-color-primary);
        transform: scale(1.05);
      }
    }
    .el-avatar {
      padding: 12px;
      margin-bottom: 12px;
      background: rgb(255 255 255 / 20%);
      border: 2px solid rgb(255 255 255 / 50%);
      border-radius: 16px !important;
      box-shadow: 0 4px 16px rgb(0 0 0 / 10%);
      transition: all 0.3s ease;
    }
    .el-text {
      margin-top: 8px;
      font-size: 15px;
      font-weight: 600;
      color: rgb(0 0 0 / 80%);
      text-shadow: 0 1px 2px rgb(255 255 255 / 20%);
    }
  }
}

// 对话框样式
:deep(.el-dialog) {
  z-index: 2001 !important; /* 确保对话框显示在最上层，高于页面标签栏 */
  overflow: hidden;
  border-radius: 16px;
  .el-dialog__header {
    padding: 20px 24px;
    margin: 0;
    background: var(--el-bg-color-overlay);
    border-bottom: 1px solid var(--el-border-color-lighter);
    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
    }
  }
  .el-dialog__body {
    padding: 24px;
  }
  .el-dialog__footer {
    padding: 16px 24px;
    background: var(--el-bg-color-overlay);
    border-top: 1px solid var(--el-border-color-lighter);
  }
}
.ip-input {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.ip-part {
  margin-right: 2px; /* 调整输入框间距 */
}
.split {
  margin: 0 2px;
  font-size: 16px;
  color: #666666;
}
.el-input {
  width: 60px;
}
.el-button {
  margin-left: 10px;
}
.ip-search-container {
  padding: 10px;
  margin: 20px;
}

/* 黑暗模式样式 */
html.dark {
  .tools-container {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d3748 100%);

    /* 装饰元素 - 黑暗模式 */
    &::before {
      background: radial-gradient(circle, rgb(100 100 100 / 50%) 0%, rgb(100 100 100 / 0%) 70%);
    }
    &::after {
      background: radial-gradient(circle, rgb(70 130 180 / 30%) 0%, rgb(70 130 180 / 0%) 70%);
    }
  }
  .table-box {
    .avatar-div {
      /* 毕玻璃效果 - 黑暗模式 */
      background: rgb(30 30 30 / 30%);
      border: 1px solid rgb(255 255 255 / 10%);
      box-shadow: 0 8px 32px rgb(0 0 0 / 20%);
      &:hover {
        background: rgb(40 40 40 / 40%);
        box-shadow: 0 12px 32px rgb(0 0 0 / 30%);
      }
      .el-avatar {
        background: rgb(40 40 40 / 50%);
        border: 2px solid rgb(255 255 255 / 20%);
        box-shadow: 0 4px 16px rgb(0 0 0 / 20%);
      }
      .el-text {
        color: var(--el-text-color-primary);
        text-shadow: 0 1px 2px rgb(0 0 0 / 30%);
      }
    }
  }

  /* 对话框样式 - 黑暗模式 */
  :deep(.el-dialog) {
    z-index: 2001 !important; /* 确保对话框显示在最上层，高于页面标签栏 */
    background: rgb(30 30 30 / 70%);
    border: 1px solid rgb(255 255 255 / 10%);
    box-shadow: 0 8px 32px rgb(0 0 0 / 30%);
    .el-dialog__header {
      background: rgb(40 40 40 / 50%);
      border-bottom: 1px solid rgb(255 255 255 / 10%);
    }
    .el-dialog__body {
      background: rgb(30 30 30 / 30%);
    }
    .el-dialog__footer {
      background: rgb(40 40 40 / 50%);
      border-top: 1px solid rgb(255 255 255 / 10%);
    }
  }

  /* 其他元素样式 - 黑暗模式 */
  .split {
    color: var(--el-text-color-secondary);
  }
}
